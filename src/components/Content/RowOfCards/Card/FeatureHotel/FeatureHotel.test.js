import React from 'react';
import { render, screen } from '@testing-library/react';
import FeatureHotel from './FeatureHotel';
import { useDataLayer } from 'hooks/useDataLayer';
import { mockFeatureHotel } from 'components/Content/RowOfCards/mocks';
import mockStore from 'redux-mock-store';
import { Provider } from 'react-redux';
import { ThemeProvider } from 'emotion-theming';
import { theme } from '@qga/roo-ui';

jest.mock('hooks/useDataLayer');
jest.mock('../../../BlockMarkdown/BlockMarkdown', () => jest.fn(() => <div data-testid="block-markdown-mock" />));
const emitInteractionEvent = jest.fn();

beforeEach(() => {
  jest.clearAllMocks();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

const configureMockStore = mockStore([]);
const store = configureMockStore({});

const renderWithProviders = (props = {}) => {
  const allProps = { ...mockFeatureHotel, ...props };
  return render(
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <FeatureHotel {...allProps} />
      </ThemeProvider>
    </Provider>,
  );
};

describe('FeatureHotel', () => {
  it('renders the image placeholder if a hotel has no images', () => {
    const hotelWithNoImages = {
      ...mockFeatureHotel,
      property: {
        ...mockFeatureHotel.property,
        images: undefined,
      },
    };
    renderWithProviders(hotelWithNoImages);
    expect(screen.getByTestId('image-fallback')).toBeInTheDocument();
  });

  it('renders the property name and suburb', () => {
    renderWithProviders();
    expect(screen.getByTestId('property-name')).toHaveTextContent('The Langham Melbourne');
    expect(screen.getByTestId('property-suburb')).toHaveTextContent('Southbank');
  });

  it('renders the BlockMarkdown with mdContent and callToAction', () => {
    renderWithProviders();

    const blockMarkdownEl = screen.getByTestId('block-markdown-mock');
    expect(blockMarkdownEl).toBeInTheDocument();

    const BlockMarkdownMock = require('../../../BlockMarkdown/BlockMarkdown');
    expect(BlockMarkdownMock).toHaveBeenCalledWith(
      expect.objectContaining({
        // eslint-disable-next-line testing-library/no-node-access -- accessing test variable value only
        mdContent: mockFeatureHotel.content[0].children[0].text,
        callToAction: mockFeatureHotel.callToAction,
      }),
      expect.anything(),
    );
  });

  it('when no promotion, does not render PromotionalSash', () => {
    renderWithProviders();
    expect(screen.queryByText(/promotional/i)).not.toBeInTheDocument();
  });

  it('with a promotion, shows PromotionalSash with the promotion name', () => {
    renderWithProviders({ promotionName: 'Mantra Sale' });
    expect(screen.getByText('Mantra Sale')).toBeInTheDocument();
  });

  it('renders image gallery with photos', () => {
    renderWithProviders({ cardCount: 2 });
    const slideButton = screen.getByLabelText('Go to Slide 1');
    expect(slideButton).toBeInTheDocument();

    // get direct child of slidebutton
    // eslint-disable-next-line testing-library/no-node-access -- testing internal div of ImageGallery component
    const slideButtonChild = slideButton.firstChild;
    expect(slideButtonChild).toBeInTheDocument();
    const style = window.getComputedStyle(slideButtonChild);
    expect(style.height).toBe('220px');
  });

  it('displays ImageGallery with two hotel layout', () => {
    renderWithProviders({ cardCount: 2 });
    expect(screen.getByRole('button', { name: 'View 2 photos' })).toBeInTheDocument();
  });

  describe('when the content does not contain terms and conditions', () => {
    it('does not render BlockMarkdown with a disclaimerUrl', () => {
      renderWithProviders();
      const BlockMarkdownMock = require('../../../BlockMarkdown/BlockMarkdown');
      expect(BlockMarkdownMock).toHaveBeenCalledWith(
        expect.objectContaining({
          termsAndConditionsUrl: undefined,
        }),
        expect.anything(),
      );
    });

    it('renders BlockMarkdown with a disclaimerUrl if termsUrl is provided', () => {
      renderWithProviders({ termsUrl: '#terms-and-conditions' });
      const BlockMarkdownMock = require('../../../BlockMarkdown/BlockMarkdown');
      expect(BlockMarkdownMock).toHaveBeenCalledWith(
        expect.objectContaining({
          termsAndConditionsUrl: '#terms-and-conditions',
        }),
        expect.anything(),
      );
    });
  });
});
