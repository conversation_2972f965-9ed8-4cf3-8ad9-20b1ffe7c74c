import React from 'react';
import { useRouter } from 'next/router';
import { Provider } from 'react-redux';
import { configureStore } from 'redux-mock-store';
import { render, screen } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import ExclusiveOfferCrossSellBanner from './ExclusiveOfferCrossSellBanner';
import { useDataLayer } from 'hooks/useDataLayer';
import { getPropertyId } from 'store/property/propertySelectors';
import { getQueryParams } from 'store/router/routerSelectors';

jest.mock('next/router', () => ({ useRouter: jest.fn() }));
jest.mock('hooks/useDataLayer');
jest.mock('store/property/propertySelectors');
jest.mock('store/router/routerSelectors');

const emitInteractionEvent = jest.fn();
const mockRouter = { push: jest.fn() };
const mockStore = configureStore();

describe('ExclusiveOfferCrossSellBanner', () => {
  beforeAll(() => {
    jest.clearAllMocks();
    getPropertyId.mockReturnValue('123');
    getQueryParams.mockReturnValue({ query: 'param' });
    useDataLayer.mockReturnValue({ emitInteractionEvent });
    useRouter.mockReturnValue(mockRouter);
  });

  const renderComponent = () => {
    return render(
      <Provider store={mockStore()}>
        <ExclusiveOfferCrossSellBanner />
      </Provider>,
    );
  };

  it('renders the banner with correct content', () => {
    renderComponent();

    expect(screen.getByText('Want to treat yourself with extra special inclusions & great savings?')).toBeInTheDocument();
    expect(screen.getByAltText('Food & wine logo')).toBeInTheDocument();
    expect(
      screen.getByText('There are limited time special offers available for this hotel for specific dates or nights stay.'),
    ).toBeInTheDocument();
    expect(screen.getByText('View Exclusive Offers')).toBeInTheDocument();
  });

  it('renders the banner with correct styles', () => {
    renderComponent();

    const banner = screen.getByTestId('exclusive-offer-banner');
    expect(banner).toMatchSnapshot();
  });

  describe('when clicking the call to action', () => {
    it('emits an event to the data layer and redirects the user', async () => {
      renderComponent();

      const ctaButton = screen.getByText('View Exclusive Offers');

      await userEvent.click(ctaButton);

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Exclusive Offers Cross Sell Banner',
        value: 'View Exclusive Offers Selected',
      });

      expect(mockRouter.push).toHaveBeenCalledWith({
        pathname: '/properties/123/exclusive-offers',
        query: { query: 'param' },
      });
    });
  });
});
