// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`ExclusiveOfferCrossSellBanner renders the banner with correct styles 1`] = `
<div
  class="css-v8xs4x-Box e1m6xhuh0"
  data-testid="exclusive-offer-banner"
>
  <div
    class="css-1rupnjr-Box-Card e19a0egr0"
  >
    <img
      alt="Food & wine logo"
      class="css-mv9ju5-Image eytf1ce0"
      src="restaurant.svg"
    />
    <div
      class="css-xg7eu7-Box-Flex-BannerContainer e1vpzwpc3"
    >
      <div
        class="css-er664p-Box-Flex e1pfwvfi0"
      >
        <span
          class="css-1ioodby-Text-HeadingWithLink e1vpzwpc1"
        >
          Want to treat yourself with extra special inclusions & great savings?
        </span>
        <span
          class="css-1jd8ohe-Text-ContentWithLink e1vpzwpc2"
        >
          <div
            class="css-1jkuu4z-Box e1m6xhuh0"
          >
            There are limited time special offers available for this hotel for specific dates or nights stay.
          </div>
        </span>
      </div>
      <div
        class="css-eoiy4n-Box-Flex e1pfwvfi0"
      >
        <button
          class="css-1wdip5r-NakedButton-CustomNakedButton e1vpzwpc0"
          type="button"
          width="100%"
        >
          View Exclusive Offers
          <svg
            class="en7chz91 css-1lru2pa-StyledSvg-Icon en7chz90"
            fill="currentcolor"
            title="arrowForward"
            viewBox="0 0 24 24"
          >
            <path />
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>
`;
