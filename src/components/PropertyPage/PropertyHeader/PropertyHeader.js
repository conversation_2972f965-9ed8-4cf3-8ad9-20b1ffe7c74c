import React, { memo, useCallback, useState, Fragment } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { getIsExclusive, getIsLoading } from 'store/property/propertySelectors';
import { Heading, Box, Flex, Hide, StarRating, NakedButton } from '@qga/roo-ui/components';
import TripAdvisorRating from 'components/TripAdvisorRating';
import TripAdvisorReviewsModal from 'components/TripAdvisorReviewsModal';
import { useModal } from 'lib/hooks';
import { useDataLayer } from 'hooks/useDataLayer';
import FromPrice from 'components/PropertyPage/FromPrice';
import ExclusiveContactDetails from 'components/PropertyPage/ExclusiveContactDetails';
import { getExclusiveOffer, getIsLoading as getIsLoadingExclusiveOffers } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import ResultLoader from 'components/Loader/ResultLoader';
import LoaderSkeletonCard from './LoaderSkeletonCard';
import useRatingTooltip from 'hooks/useRatingTooltip';
import useTripadvisorModal from 'hooks/optimizely/useTripadvisorModal';
import { includes, isEmpty } from 'lodash';

const LoadedPropertyHeader = ({ property, tripAdvisorRating, ...rest }) => {
  const isExclusive = useSelector(getIsExclusive);
  const [isRatingOpen, setIsRatingOpen] = useState(false);
  const { name, id, rating, ratingType } = property;
  const { openModal, modalProps } = useModal();
  const { emitInteractionEvent } = useDataLayer();
  const hasTripAdvisorRating = tripAdvisorRating && tripAdvisorRating.reviewCount > 0;

  const { ignoredList, isReady, isTripadvisorModal } = useTripadvisorModal();
  const isIgnoredId = !isEmpty(ignoredList) && includes(ignoredList, Number(id));
  const displayReviews = isReady && isTripadvisorModal && !isIgnoredId;

  const ratingTooltip = useRatingTooltip({ ratingType });

  const handleOnClick = useCallback(() => {
    openModal();
    emitInteractionEvent({ type: 'Tripadvisor Pop Up', value: 'Top Link Selected' });
  }, [emitInteractionEvent, openModal]);
  const notZeroRating = rating !== 0;

  return (
    <Box {...rest}>
      <Flex flexDirection={['column', 'row', 'row']} justifyContent={['inherit', 'space-between', 'space-between']} mb={[0, 2, 7]}>
        <Flex flexDirection="column" ml={[0, 3, 0]} pb={[1, 4, 0]} width="100%">
          <Heading.h1
            data-testid="property-name"
            fontSize={['lg', 'lg', '2xl']}
            mr={[3, 3, 0]}
            ml={!isExclusive ? [3, 0, 0] : [3, 3, 0]}
            lineHeight="tight"
            mb={4}
            fontWeight="normal"
          >
            {name}
          </Heading.h1>
          <Flex alignItems="flex-start">
            {notZeroRating && (
              <Box
                mr={3}
                ml={!isExclusive ? [3, 0, 0] : [3, 3, 0]}
                height={isRatingOpen ? [ratingType === 'AAA' ? 130 : 100, 'auto'] : 'auto'}
              >
                <StarRating
                  size={20}
                  rating={rating}
                  ratingType={ratingType}
                  {...ratingTooltip}
                  onTooltipChange={(isOpen) => setIsRatingOpen(isOpen)}
                />
              </Box>
            )}
            {hasTripAdvisorRating && (
              <Fragment>
                <NakedButton onClick={displayReviews ? handleOnClick : undefined} data-testid="trip-advisor-button">
                  <TripAdvisorRating
                    rating={tripAdvisorRating}
                    displayReviews={displayReviews}
                    ml={notZeroRating ? '' : '-6px'}
                    underlined
                  />
                </NakedButton>
                {displayReviews && <TripAdvisorReviewsModal {...modalProps} locationId={id} />}
              </Fragment>
            )}
          </Flex>
        </Flex>
        {!isExclusive && <FromPrice />}
        {isExclusive && (
          <Hide xs>
            <ExclusiveContactDetails />{' '}
          </Hide>
        )}
      </Flex>
    </Box>
  );
};

LoadedPropertyHeader.propTypes = {
  property: PropTypes.shape({
    name: PropTypes.string,
    rating: PropTypes.number,
    ratingType: PropTypes.string,
    latitude: PropTypes.number,
    longitude: PropTypes.number,
    id: PropTypes.string,
    address: PropTypes.shape({
      streetAddress: PropTypes.arrayOf(PropTypes.string),
      suburb: PropTypes.string,
      state: PropTypes.string,
      postcode: PropTypes.string,
      country: PropTypes.string,
    }),
  }).isRequired,
  tripAdvisorRating: PropTypes.object,
};

LoadedPropertyHeader.defaultProps = {
  tripAdvisorRating: undefined,
};

const PropertyHeader = memo(({ property, tripAdvisorRating }) => {
  const isExclusiveOffer = useSelector(getExclusiveOffer);
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const isLoading = isExclusiveOffer ? useSelector(getIsLoadingExclusiveOffers) : useSelector(getIsLoading);

  return (
    <Box bg="white" pt={[6, 10, 10]} data-testid="property-header">
      <ResultLoader isLoading={isLoading} skeletonResultCount={1} skeletonCardComponent={LoaderSkeletonCard}>
        <LoadedPropertyHeader property={property} tripAdvisorRating={tripAdvisorRating} />
      </ResultLoader>
    </Box>
  );
});

PropertyHeader.displayName = 'PropertyHeader';

PropertyHeader.propTypes = {
  property: PropTypes.shape({
    name: PropTypes.string,
    rating: PropTypes.number,
    ratingType: PropTypes.string,
    latitude: PropTypes.number,
    longitude: PropTypes.number,
    id: PropTypes.string,
    address: PropTypes.shape({
      streetAddress: PropTypes.arrayOf(PropTypes.string),
      suburb: PropTypes.string,
      state: PropTypes.string,
      postcode: PropTypes.string,
      country: PropTypes.string,
    }),
  }).isRequired,
  tripAdvisorRating: PropTypes.object,
};

export default PropertyHeader;
