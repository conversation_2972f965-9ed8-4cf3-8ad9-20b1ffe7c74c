import React from 'react';
import { render, screen, within, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import configureMockStore from 'redux-mock-store';
import { ThemeProvider } from 'emotion-theming';
import { mocked } from 'test-utils';
import NavigationMenu from './NavigationMenu';
import { useDataLayer } from 'hooks/useDataLayer';
import { useMediaQuery } from 'react-responsive';
import { theme } from '@qga/roo-ui';

jest.mock('hooks/useDataLayer');
jest.mock('react-responsive');

const mockStore = configureMockStore();

const props: React.ComponentProps<typeof NavigationMenu> = {
  children: 'Sydney',
  items: [
    {
      name: 'Most Popular',
      regions: [
        {
          fullName: 'Gold Coast, QLD, Australia',
          id: '51782',
          name: 'Gold Coast',
          slug: 'gold-coast',
        },
        {
          fullName: 'Melbourne, VIC, Australia',
          id: '51770',
          name: 'Melbourne',
          slug: 'melbourne',
        },
      ],
    },
  ],
  dealType: 'bonus-points',
};

const renderComponent = (componentProps = props) => {
  const store = mockStore({});
  return render(
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <NavigationMenu {...componentProps} />
      </ThemeProvider>
    </Provider>,
  );
};

let originalConsoleError: typeof console.error;

beforeAll(() => {
  originalConsoleError = console.error;
  jest.spyOn(console, 'error').mockImplementation(() => {});
});

afterAll(() => {
  console.error = originalConsoleError;
});

const emitInteractionEvent = jest.fn();

const setupMocks = (isMobile = false) => {
  jest.clearAllMocks();
  mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
  mocked(useMediaQuery).mockReturnValue(isMobile);
};

const openMenu = async (user, triggerTestId) => {
  await userEvent.click(screen.getByTestId(triggerTestId));
};

const testRegionLinks = (triggerTestId, linksTestId) => {
  for (const item of props.items) {
    for (const region of item.regions) {
      describe(region.name, () => {
        it('renders the link', async () => {
          renderComponent();
          await openMenu(userEvent, triggerTestId);

          await waitFor(() => {
            const linksContainer = screen.getByTestId(linksTestId);
            const link = within(linksContainer).getByText(region.name);
            expect(link).toHaveAttribute('href', `/hotels/deals/${region.slug}/bonus-points`);
          });
        });

        describe('when clicking the link', () => {
          it('emits an event to the data layer', async () => {
            renderComponent();
            await openMenu(userEvent, triggerTestId);

            const linksContainer = await screen.findByTestId(linksTestId);
            const link = within(linksContainer).getByText(region.name);

            link.addEventListener('click', (e) => e.preventDefault());

            await userEvent.click(link);

            await waitFor(() => {
              expect(emitInteractionEvent).toHaveBeenCalledWith({
                type: 'Navigation Menu',
                value: `${region.fullName} Selected`,
              });
            });
          });
        });
      });
    }
  }
};

describe('<NavigationMenu /> Desktop', () => {
  beforeEach(() => setupMocks(false));

  it('renders with expected props', () => {
    renderComponent();
    expect(screen.getByTestId('desktop-nav-trigger-button')).toBeInTheDocument();
  });

  it('renders the <MenuButton />', () => {
    renderComponent();
    expect(screen.getByTestId('desktop-nav-trigger-button')).toBeInTheDocument();
    expect(screen.getByText('Sydney')).toBeInTheDocument();
  });

  it('renders the <Accordion />', async () => {
    renderComponent();
    await openMenu(userEvent, 'desktop-nav-trigger-button');

    await waitFor(() => {
      expect(screen.getByTestId('desktop-nav-menu-links')).toBeInTheDocument();
    });
  });

  for (const item of props.items) {
    it('renders <AccordionItem />', async () => {
      renderComponent();
      await openMenu(userEvent, 'desktop-nav-trigger-button');

      await waitFor(() => {
        expect(screen.getByText(item.name)).toBeInTheDocument();
      });
    });

    it('renders <AccordionButton />', async () => {
      renderComponent();
      await openMenu(userEvent, 'desktop-nav-trigger-button');

      await waitFor(() => {
        expect(screen.getByText(item.name)).toBeInTheDocument();
      });
    });
  }

  testRegionLinks('desktop-nav-trigger-button', 'desktop-nav-menu-links');
});

describe('<NavigationMenu /> Mobile', () => {
  beforeEach(() => setupMocks(true));

  it('renders with expected props', () => {
    renderComponent();
    expect(screen.getByTestId('mobile-nav-trigger-button')).toBeInTheDocument();
  });

  it('renders the <DisclosureButton />', () => {
    renderComponent();
    expect(screen.getByTestId('mobile-nav-trigger-button')).toBeInTheDocument();
    expect(screen.getByText('Sydney')).toBeInTheDocument();
  });

  it('can open and close the modal', async () => {
    renderComponent();
    await openMenu(userEvent, 'mobile-nav-trigger-button');

    await waitFor(() => {
      expect(screen.getByText('Select a destination')).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByTestId('close-modal-button')).toBeInTheDocument();
    });

    const closeButton = screen.getByTestId('close-modal-button');

    await userEvent.click(closeButton);

    expect(closeButton).toBeInTheDocument();
  });

  it('renders the Accordion', async () => {
    renderComponent();
    await openMenu(userEvent, 'mobile-nav-trigger-button');

    await waitFor(() => {
      expect(screen.getByTestId('nav-menu-links')).toBeInTheDocument();
    });
  });

  for (const item of props.items) {
    it('renders <AccordionItem />', async () => {
      renderComponent();
      await openMenu(userEvent, 'mobile-nav-trigger-button');

      await waitFor(() => {
        expect(screen.getByText(item.name)).toBeInTheDocument();
      });
    });

    it('renders <AccordionButton />', async () => {
      renderComponent();
      await openMenu(userEvent, 'mobile-nav-trigger-button');

      await waitFor(() => {
        expect(screen.getByText(item.name)).toBeInTheDocument();
      });
    });
  }

  testRegionLinks('mobile-nav-trigger-button', 'nav-menu-links');
});

describe('when the dealType.code is all_deals', () => {
  beforeEach(() => setupMocks(false));

  it('renders a valid link', async () => {
    const newProps = { ...props, dealType: '' };
    renderComponent(newProps);
    await openMenu(userEvent, 'desktop-nav-trigger-button');

    const region = {
      fullName: 'Gold Coast, QLD, Australia',
      id: '51782',
      name: 'Gold Coast',
      slug: 'gold-coast',
    };

    await waitFor(() => {
      const linksContainer = screen.getByTestId('desktop-nav-menu-links');
      const link = within(linksContainer).getByText(region.name);
      expect(link).toHaveAttribute('href', `/hotels/deals/${region.slug}/`);
    });
  });
});
