import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import Currency from './Currency';
import { FormatNumber } from 'components/formatters';
import { Decimal } from 'decimal.js';

jest.mock('components/formatters');

const defaultProps = {
  amount: '71.6',
  roundToCeiling: false,
  hideCurrency: false,
};

const customRender = (props = {}) => render(<Currency {...defaultProps} {...props} />);

beforeEach(() => {
  jest.clearAllMocks();
  (FormatNumber as jest.Mock).mockReturnValue('foo');
});

describe('when in cash mode', () => {
  it('shows the currency text', () => {
    customRender({ currency: 'AUD' });
    expect(screen.getByTestId('currency-text-unhidden')).toHaveTextContent('AUD');
  });

  it('renders the right style for Wrapper (reverse false)', () => {
    customRender({ currency: 'AUD' });
    const wrapper = screen.getByTestId('currency');
    expect(window.getComputedStyle(wrapper).flexFlow).toBe('row');
  });

  it('parses an amount string into a float', () => {
    customRender({ currency: 'AUD', amount: '$10,550AUD' });
    expect(FormatNumber).toHaveBeenCalledWith({ number: new Decimal(10550), decimalPlaces: 2 });
  });

  it('renders the right style for Wrapper (reverse true)', () => {
    customRender({ currency: 'AUD', alignCurrency: 'left' });
    const wrapper = screen.getByTestId('currency');
    expect(window.getComputedStyle(wrapper).flexFlow).toBe('row-reverse');
  });

  it('renders the price rounded', () => {
    customRender({ currency: 'AUD', roundToCeiling: true });
    expect(FormatNumber).toHaveBeenCalledWith({ number: new Decimal(72), decimalPlaces: 0 });
  });

  it('renders the price not rounded', () => {
    customRender({ currency: 'AUD' });
    expect(FormatNumber).toHaveBeenCalledWith({ number: new Decimal(71.6), decimalPlaces: 2 });
  });

  it('shows the currency when hideCurrency is false', () => {
    customRender({ currency: 'AUD', hideCurrency: false });
    expect(screen.getByTestId('currency-text-unhidden')).toHaveTextContent('AUD');
  });

  it('does not show the currency when hideCurrency is true', () => {
    customRender({ currency: 'AUD', hideCurrency: true });
    expect(screen.queryByTestId('currency-text-unhidden')).not.toBeInTheDocument();
  });

  it('displays the currency symbol', async () => {
    customRender({ currency: 'AUD' });
    expect(await screen.findByTestId('currency-symbol-not-from-price')).toHaveTextContent('$');
  });

  it('displays the T&Cs symbol', async () => {
    customRender({ currency: 'AUD', amount: '100', termsConditionsSymbol: '*' });
    expect(await screen.findByTestId('terms-conditions-symbol')).toHaveTextContent('*');
  });
});

describe('when in points mode', () => {
  it('does not round the amount', () => {
    customRender({ amount: '9900', currency: 'PTS' });
    expect(FormatNumber).toHaveBeenCalledWith({ number: new Decimal(9900), decimalPlaces: 0 });
  });

  it('shows the points currency text', () => {
    customRender({ amount: '9900', currency: 'PTS' });
    expect(screen.getByTestId('currency-text-unhidden')).toHaveTextContent('PTS');
  });

  it('does not display any currency symbol', () => {
    customRender({ amount: '9900', currency: 'PTS' });
    expect(screen.queryByTestId('currency-symbol')).not.toBeInTheDocument();
    expect(screen.queryByTestId('currency-symbol-not-from-price')).not.toBeInTheDocument();
  });

  it('displays the T&Cs symbol', async () => {
    customRender({ currency: 'PTS', amount: '9900', termsConditionsSymbol: '*' });
    expect(await screen.findByTestId('terms-conditions-symbol')).toHaveTextContent('*');
  });
});

describe('when total is null', () => {
  it('does not render', () => {
    render(<Currency amount={undefined} currency={undefined} />);
    expect(screen.queryByTestId('currency-text-unhidden')).not.toBeInTheDocument();
    expect(screen.queryByTestId('currency')).not.toBeInTheDocument();
  });
});

describe('when amount is a decimal', () => {
  it('renders the amount', () => {
    customRender({ currency: 'AUD', amount: new Decimal(100.11) });
    expect(FormatNumber).toHaveBeenCalledWith({ number: new Decimal(100.11), decimalPlaces: 2 });
  });
});
