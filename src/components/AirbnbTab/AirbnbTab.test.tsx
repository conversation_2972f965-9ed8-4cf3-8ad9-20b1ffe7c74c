/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { mocked } from 'test-utils';
import '@testing-library/jest-dom';
import { Provider } from 'react-redux';
import store from 'redux-mock-store';
import AirbnbTab from './AirbnbTab';
import { getQueryParams } from 'store/router/routerSelectors';
import { addDays } from 'lib/date';
import stringifyQueryValues from 'lib/search/stringifyQueryValues';

interface MockLocationAutocompleterProps {
  updateQuery: (query: { location: string }) => void;
  locationName?: string;
  [key: string]: unknown;
}
interface MockOccupantPickerProps {
  updateQuery: (query: { adults: number }) => void;
}

interface MockAvailabilityDatePickerProps {
  updateQuery: (query: { checkIn?: string; checkOut?: string }) => void;
  clearSelectedDates: () => void;
}

jest.mock('@qga/roo-ui/components', () => ({
  Box: jest.fn(({ children, ...props }) => <div {...props}>{children}</div>),
  Flex: jest.fn(({ children, flexDirection, alignItems, flexWrap, ...props }) => <div {...props}>{children}</div>),
  Text: jest.fn(({ children, ...props }) => <span {...props}>{children}</span>),
  Button: jest.fn(({ children, as: Component = 'button', ...props }) => <Component {...props}>{children}</Component>),
  Link: jest.fn(({ children, onClick }) => (
    <a data-testid="mock-link" onClick={onClick} href="/local">
      {children}
    </a>
  )),
}));

jest.mock('components/LocationAutocompleter', () => {
  const MockLocationAutocompleter = ({
    updateQuery,
    locationName,
    labelOptions,
    error,
    returnProperties,
    isOptional,
    limit,
    ...props
  }: MockLocationAutocompleterProps) => (
    <input
      data-testid="location-autocompleter"
      onChange={(e) => updateQuery({ location: e.target.value })}
      value={locationName || ''}
      data-limit={limit}
      {...props}
    />
  );
  return MockLocationAutocompleter;
});

jest.mock('components/OccupantPicker', () => {
  const MockOccupantPicker = ({ updateQuery }: MockOccupantPickerProps) => (
    <div data-testid="occupant-picker-mock">
      <button onClick={() => updateQuery({ adults: 3 })}>Add Adult</button>
    </div>
  );
  return MockOccupantPicker;
});

jest.mock('components/AvailabilityDatePicker/DatePicker', () => {
  const MockAvailabilityDatePicker = ({ updateQuery, clearSelectedDates }: MockAvailabilityDatePickerProps) => (
    <div data-testid="availability-date-picker-mock">
      <input data-testid="checkin-date-input" onChange={(e) => updateQuery({ checkIn: e.target.value })} />
      <input data-testid="checkout-date-input" onChange={(e) => updateQuery({ checkOut: e.target.value })} />
      <button onClick={clearSelectedDates}>Clear Dates</button>
    </div>
  );
  return MockAvailabilityDatePicker;
});

jest.mock('components/QffWidget/QffWidget', () => () => <div data-testid="qff-widget-mock" />);

jest.mock('lib/date', () => ({
  addDays: jest.fn((date, days) => {
    const result = new Date(date);
    result.setDate(date.getDate() + days);
    return result;
  }),
  dateFromString: jest.fn((dateStr) => {
    if (!dateStr) return undefined;
    return new Date(dateStr);
  }),
}));

jest.mock('lib/search/stringifyQueryValues', () =>
  jest.fn((query) => {
    const { location, guests, checkin, checkout } = query;
    return `location=${location || ''}&guests=${guests || ''}&checkin=${checkin ? 'mockCheckInDate' : ''}&checkout=${checkout ? 'mockCheckoutDate' : ''}`;
  }),
);

jest.mock('lodash/capitalize', () =>
  jest.fn((str) => {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1);
  }),
);

const mockUseDataLayer = jest.fn();
jest.mock('hooks/useDataLayer', () => ({
  useDataLayer: () => ({
    emitInteractionEvent: mockUseDataLayer,
  }),
}));

const mockUseBreakpoints = jest.fn();
jest.mock('hooks/useBreakpoints', () => ({
  useBreakpoints: () => mockUseBreakpoints(),
}));

const mockUseIsAuthenticated = jest.fn();
jest.mock('lib/oauth', () => ({
  useIsAuthenticated: () => mockUseIsAuthenticated(),
}));

const mockHandleAirbnbRedirect = jest.fn();
jest.mock('components/AirbnbPage/AirbnbRedirect/useAirbnbRedirect', () => {
  return jest.fn(() => ({
    handleAirbnbRedirect: mockHandleAirbnbRedirect,
  }));
});

const mockStore = store([]);
const initialState = {
  router: {
    query: {},
  },
};

jest.mock('store/router/routerSelectors', () => {
  const getQueryParams = jest.fn((state) => {
    const query = state?.router?.query || {};
    return {
      location: query.location || null,
      checkIn: query.checkIn ? new Date(query.checkIn) : new Date(Date.now()),
      checkOut: query.checkOut ? new Date(query.checkOut) : new Date(Date.now() + 24 * 60 * 60 * 1000),
      adults: query.adults ? Number(query.adults) : 2,
      // eslint-disable-next-line testing-library/no-node-access -- test utility for mocking children only
      children: query.children ? Number(query.children) : 0,
      infants: query.infants ? Number(query.infants) : 0,
      payWith: query.payWith || undefined,
      id: query.id || undefined,
    };
  });

  return {
    getQueryParams,
  };
});

describe('AirbnbTab', () => {
  let store;

  beforeEach(() => {
    store = mockStore(initialState);
    mockUseDataLayer.mockClear();
    mockUseIsAuthenticated.mockClear();
    mockUseBreakpoints.mockClear();
    mockHandleAirbnbRedirect.mockClear();
    mocked(getQueryParams).mockClear();
  });

  it('renders correctly for authenticated desktop user on non-landing page', () => {
    mocked(getQueryParams).mockReturnValue({
      location: null,
      checkIn: new Date(),
      checkOut: new Date(),
      adults: 2,
      children: 0,
      infants: 0,
    });
    mockUseIsAuthenticated.mockReturnValue(true);
    mockUseBreakpoints.mockReturnValue({
      isLessThanBreakpoint: jest.fn(() => false),
      isGreaterThanOrEqualToBreakpoint: jest.fn(() => true),
    });

    render(
      <Provider store={store}>
        <AirbnbTab isLandingPage={false} />
      </Provider>,
    );

    expect(screen.getByText('Book an Airbnb and earn 1 Qantas Point per A$1 spent++')).toBeInTheDocument();
    expect(screen.getByTestId('location-autocompleter')).toBeInTheDocument();
    expect(screen.getByTestId('availability-date-picker-mock')).toBeInTheDocument();
    expect(screen.getByTestId('occupant-picker-mock')).toBeInTheDocument();
    expect(screen.getByTestId('search-airbnb-cta')).toBeInTheDocument();
    expect(screen.getByText('Search Airbnb')).toBeInTheDocument();
    expect(screen.queryByText('Earn 1 Qantas Point per A$1 spent++')).not.toBeInTheDocument();
  });

  it('renders correctly for authenticated mobile user on non-landing page', () => {
    mocked(getQueryParams).mockReturnValue({
      location: null,
      checkIn: new Date(),
      checkOut: new Date(),
      adults: 2,
      children: 0,
      infants: 0,
    });
    mockUseIsAuthenticated.mockReturnValue(true);
    mockUseBreakpoints.mockReturnValue({
      isLessThanBreakpoint: jest.fn((breakpoint) => breakpoint === 0),
      isGreaterThanOrEqualToBreakpoint: jest.fn(() => false),
    });

    render(
      <Provider store={store}>
        <AirbnbTab isLandingPage={false} />
      </Provider>,
    );

    expect(screen.getByText('Earn 1 Qantas Point per A$1 spent++')).toBeInTheDocument();
    expect(screen.queryByText('Book an Airbnb and earn 1 Qantas Point per A$1 spent++')).not.toBeInTheDocument();
  });

  test('renders correctly when it is a landing page', () => {
    mocked(getQueryParams).mockReturnValue({
      location: null,
      checkIn: new Date(),
      checkOut: new Date(),
      adults: 2,
      children: 0,
      infants: 0,
    });
    mockUseIsAuthenticated.mockReturnValue(true);
    mockUseBreakpoints.mockReturnValue({
      isLessThanBreakpoint: jest.fn(() => false),
      isGreaterThanOrEqualToBreakpoint: jest.fn(() => true),
    });

    render(
      <Provider store={store}>
        <AirbnbTab isLandingPage={true} />
      </Provider>,
    );

    expect(screen.queryByText('Book an Airbnb and earn 1 Qantas Point per A$1 spent++')).not.toBeInTheDocument();
    expect(screen.queryByText('Earn 1 Qantas Point per A$1 spent++')).not.toBeInTheDocument();
  });

  test('LocationAutocompleter updates query and emits GA event', async () => {
    mocked(getQueryParams).mockReturnValue({
      location: null,
      checkIn: new Date(),
      checkOut: new Date(),
      adults: 2,
      children: 0,
      infants: 0,
    });
    mockUseBreakpoints.mockReturnValue({
      isLessThanBreakpoint: jest.fn(() => false),
      isGreaterThanOrEqualToBreakpoint: jest.fn(() => true),
    });
    render(
      <Provider store={store}>
        <AirbnbTab isLandingPage={false} />
      </Provider>,
    );

    const locationInput = screen.getByTestId('location-autocompleter');
    await userEvent.type(locationInput, 'Sydney');

    expect(locationInput).toHaveValue('Sydney');
    expect(mockUseDataLayer).toHaveBeenCalledWith({ type: 'Location Search', value: 'Sydney Selected' });
  });

  test('OccupantPicker updates query and emits GA event', async () => {
    mocked(getQueryParams).mockReturnValue({
      location: null,
      checkIn: new Date(),
      checkOut: new Date(),
      adults: 2,
      children: 0,
      infants: 0,
    });
    mockUseBreakpoints.mockReturnValue({
      isLessThanBreakpoint: jest.fn(() => false),
      isGreaterThanOrEqualToBreakpoint: jest.fn(() => true),
    });
    render(
      <Provider store={store}>
        <AirbnbTab isLandingPage={false} />
      </Provider>,
    );

    const addAdultButton = screen.getByText('Add Adult');
    await userEvent.click(addAdultButton);

    expect(mockUseDataLayer).toHaveBeenCalledWith({ type: 'Guests Dropdown', value: 'Guests Adults 3 Selected' });
  });

  it('AvailabilityDatePicker updates query and emits GA event', async () => {
    mocked(getQueryParams).mockReturnValue({
      location: null,
      checkIn: new Date(),
      checkOut: new Date(),
      adults: 2,
      children: 0,
      infants: 0,
    });
    mockUseBreakpoints.mockReturnValue({
      isLessThanBreakpoint: jest.fn(() => false),
      isGreaterThanOrEqualToBreakpoint: jest.fn(() => true),
    });
    render(
      <Provider store={store}>
        <AirbnbTab isLandingPage={false} />
      </Provider>,
    );

    const checkInInput = screen.getByTestId('checkin-date-input');
    await userEvent.clear(checkInInput);
    await userEvent.type(checkInInput, '2025-07-20');

    expect(mockUseDataLayer).toHaveBeenCalledWith({ type: 'Date Calendar', value: 'Checkin Date Selected' });

    const checkOutInput = screen.getByTestId('checkout-date-input');
    await userEvent.clear(checkOutInput);
    await userEvent.type(checkOutInput, '2025-07-25');

    expect(mockUseDataLayer).toHaveBeenCalledWith({ type: 'Date Calendar', value: 'Checkout Date Selected' });
  });

  it('Submit button dispatches updateQuery and redirects for authenticated user', async () => {
    mockUseIsAuthenticated.mockReturnValue(true);
    mockUseBreakpoints.mockReturnValue({
      isLessThanBreakpoint: jest.fn(() => false),
      isGreaterThanOrEqualToBreakpoint: jest.fn(() => true),
    });
    const mockDate = new Date('2025-07-01T12:00:00.000Z');
    mocked(getQueryParams).mockReturnValue({
      location: null,
      checkIn: mockDate,
      checkOut: addDays(mockDate, 1),
      adults: 2,
      children: 0,
      infants: 0,
    });

    render(
      <Provider store={store}>
        <AirbnbTab isLandingPage={false} />
      </Provider>,
    );

    const searchButton = screen.getByTestId('search-airbnb-cta');
    await userEvent.click(searchButton);

    await waitFor(() => {
      const actions = store.getActions();
      expect(actions).toEqual([
        {
          type: 'search/UPDATE_QUERY',
          payload: {
            location: null,
            checkIn: mockDate,
            checkOut: new Date(mockDate.getTime() + 24 * 60 * 60 * 1000),
            adults: 2,
            children: 0,
            infants: 0,
          },
        },
      ]);
    });
    await waitFor(() => {
      expect(stringifyQueryValues).toHaveBeenCalledWith({
        location: null,
        guests: 2,
        checkin: mockDate,
        checkout: new Date(mockDate.getTime() + 24 * 60 * 60 * 1000),
      });
    });
    await waitFor(() => {
      expect(mockHandleAirbnbRedirect).toHaveBeenCalledTimes(1);
    });
    await waitFor(() => {
      expect(mockHandleAirbnbRedirect).toHaveBeenCalledWith('location=&guests=2&checkin=mockCheckInDate&checkout=mockCheckoutDate');
    });
    await waitFor(() => {
      expect(mockUseDataLayer).not.toHaveBeenCalledWith({ type: 'CTA Clicked', value: 'Continue to log in' });
    });
    jest.restoreAllMocks();
  });

  it('Submit button emits GA event for unauthenticated user', async () => {
    mocked(getQueryParams).mockReturnValue({
      location: null,
      checkIn: new Date(),
      checkOut: new Date(),
      adults: 2,
      children: 0,
      infants: 0,
    });
    mockUseIsAuthenticated.mockReturnValue(false);
    mockUseBreakpoints.mockReturnValue({
      isLessThanBreakpoint: jest.fn(() => false),
      isGreaterThanOrEqualToBreakpoint: jest.fn(() => true),
    });
    render(
      <Provider store={store}>
        <AirbnbTab isLandingPage={false} />
      </Provider>,
    );

    const searchButton = screen.getByTestId('search-airbnb-cta');
    expect(searchButton).toHaveTextContent('Continue to log in');
    await userEvent.click(searchButton);

    await waitFor(() => {
      expect(mockUseDataLayer).toHaveBeenCalledWith({ type: 'CTA Clicked', value: 'Continue to log in' });
    });
    await waitFor(() => {
      expect(mockHandleAirbnbRedirect).toHaveBeenCalledTimes(1);
    });
  });

  it('AvailabilityDatePicker clear dates functionality', async () => {
    mocked(getQueryParams).mockReturnValue({
      location: null,
      checkIn: new Date(),
      checkOut: new Date(),
      adults: 2,
      children: 0,
      infants: 0,
    });
    mockUseBreakpoints.mockReturnValue({
      isLessThanBreakpoint: jest.fn(() => false),
      isGreaterThanOrEqualToBreakpoint: jest.fn(() => true),
    });
    render(
      <Provider store={store}>
        <AirbnbTab isLandingPage={false} />
      </Provider>,
    );

    const clearDatesButton = screen.getByText('Clear Dates');
    await userEvent.click(clearDatesButton);

    expect(screen.getByTestId('checkin-date-input')).toHaveValue('');
    expect(screen.getByTestId('checkout-date-input')).toHaveValue('');
  });

  it('uses initial query params from Redux store', () => {
    const queryParams = {
      location: 'Paris',
      checkIn: '2025-09-01',
      checkOut: '2025-09-07',
      adults: 3,
      children: 1,
      infants: 0,
    };
    const storeWithQueryParams = mockStore({
      router: {
        query: queryParams,
      },
    });

    mocked(getQueryParams).mockReturnValue(queryParams);

    mockUseBreakpoints.mockReturnValue({
      isLessThanBreakpoint: jest.fn(() => false),
      isGreaterThanOrEqualToBreakpoint: jest.fn(() => true),
    });

    render(
      <Provider store={storeWithQueryParams}>
        <AirbnbTab isLandingPage={false} />
      </Provider>,
    );

    expect(screen.getByTestId('location-autocompleter')).toHaveValue('Paris');
  });

  it('getLimitCharacter returns correct limit for mobile breakpoint', () => {
    mocked(getQueryParams).mockReturnValue({
      location: null,
      checkIn: new Date(),
      checkOut: new Date(),
      adults: 2,
      children: 0,
      infants: 0,
    });
    mockUseBreakpoints.mockReturnValue({
      isLessThanBreakpoint: jest.fn((bp) => bp === 0),
      isGreaterThanOrEqualToBreakpoint: jest.fn(() => false),
    });
    render(
      <Provider store={store}>
        <AirbnbTab isLandingPage={false} />
      </Provider>,
    );
    expect(screen.getByTestId('location-autocompleter')).toHaveAttribute('data-limit', '27');
  });

  it('getLimitCharacter returns correct limit for tablet breakpoint', () => {
    mocked(getQueryParams).mockReturnValue({
      location: null,
      checkIn: new Date(),
      checkOut: new Date(),
      adults: 2,
      children: 0,
      infants: 0,
    });
    mockUseBreakpoints.mockReturnValue({
      isLessThanBreakpoint: jest.fn((bp) => {
        if (bp === 0) return false;
        if (bp === 1) return true;
        return false;
      }),
      isGreaterThanOrEqualToBreakpoint: jest.fn((bp) => {
        if (bp === 0) return true;
        return false;
      }),
    });
    render(
      <Provider store={store}>
        <AirbnbTab isLandingPage={false} />
      </Provider>,
    );
    expect(screen.getByTestId('location-autocompleter')).toHaveAttribute('data-limit', '100');
  });

  it('getLimitCharacter returns correct limit for small desktop breakpoint', () => {
    mocked(getQueryParams).mockReturnValue({
      location: null,
      checkIn: new Date(),
      checkOut: new Date(),
      adults: 2,
      children: 0,
      infants: 0,
    });
    mockUseBreakpoints.mockReturnValue({
      isLessThanBreakpoint: jest.fn((bp) => {
        if (bp === 0) return false;
        if (bp === 1) return false;
        if (bp === 2) return true;
        return false;
      }),
      isGreaterThanOrEqualToBreakpoint: jest.fn((bp) => {
        if (bp === 1) return true;
        return false;
      }),
    });
    render(
      <Provider store={store}>
        <AirbnbTab isLandingPage={false} />
      </Provider>,
    );
    expect(screen.getByTestId('location-autocompleter')).toHaveAttribute('data-limit', '30');
  });

  it('getLimitCharacter returns correct limit for large desktop breakpoint', () => {
    mocked(getQueryParams).mockReturnValue({
      location: null,
      checkIn: new Date(),
      checkOut: new Date(),
      adults: 2,
      children: 0,
      infants: 0,
    });
    mockUseBreakpoints.mockReturnValue({
      isLessThanBreakpoint: jest.fn(() => false),
      isGreaterThanOrEqualToBreakpoint: jest.fn((bp) => bp === 2),
    });
    render(
      <Provider store={store}>
        <AirbnbTab isLandingPage={false} />
      </Provider>,
    );
    expect(screen.getByTestId('location-autocompleter')).toHaveAttribute('data-limit', '35');
  });
});
