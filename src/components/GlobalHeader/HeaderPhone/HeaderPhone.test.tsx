import * as React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { mocked } from 'test-utils';
import { HeaderPhone } from './HeaderPhone';
import { remock } from 'react-remock';
import { QffMenu } from './QffMenu';
import { PhoneMenu } from './PhoneMenu';
import { LoginTooltip } from '../LoginTooltip';
import { HeaderProps, LogoProps } from '../types';
import { GlobalHeaderContextType, useGlobalHeaderContext } from '../GlobalHeaderContext';
import { getQueryParams } from 'store/router/routerSelectors';
import { configureStore } from 'redux-mock-store';
import { Provider } from 'react-redux';

jest.mock('store/router/routerSelectors');
jest.mock('../GlobalHeaderContext');

const context = {} as GlobalHeaderContextType;
const mockStore = configureStore();

const renderComponent = (props: HeaderProps) => {
  context.onInteraction = jest.fn();

  mocked(useGlobalHeaderContext).mockReturnValue(context);

  remock.mock(PhoneMenu, () => <div data-testid="mock-phone-menu"></div>);
  remock.mock(QffMenu, () => <div data-testid="mock-qff-menu"></div>);
  remock.mock(LoginTooltip, () => <div data-testid="mock-login-tooltip"></div>);

  render(
    <Provider store={mockStore({})}>
      <HeaderPhone {...props} />
    </Provider>,
  );
};

const Logo = ({ alt, src }: LogoProps) => <img data-testid="custom-logo" alt={alt} src={src} />;

let originalConsoleError: typeof console.error;
beforeAll(() => {
  originalConsoleError = console.error;
  jest.spyOn(console, 'error').mockImplementation(() => {});
});

afterAll(() => {
  console.error = originalConsoleError;
});

describe('<HeaderPhone />', () => {
  beforeEach(() => {
    mocked(getQueryParams).mockReturnValue({ location: 'Test' });
  });

  describe('if qffLoginEnabled is true', () => {
    it('renders PhoneMenu', () => {
      renderComponent({ qffLoginEnabled: true });
      expect(screen.getByTestId('mock-phone-menu')).toBeInTheDocument();
    });

    it('renders QffMenu', () => {
      renderComponent({ qffLoginEnabled: true });
      expect(screen.getByTestId('mock-qff-menu')).toBeInTheDocument();
    });

    describe('renders the Logo link', () => {
      it('renders the Logo link with the home page as default href and query strings', () => {
        renderComponent({ qffLoginEnabled: true });

        expect(screen.getByTestId('header-logo-link')).toHaveAttribute('href', '/hotels?location=Test');
      });

      it('that when clicked tracks the interaction', async () => {
        renderComponent({ qffLoginEnabled: true });
        const link = screen.queryByTestId('header-logo-link');
        link && (await userEvent.click(link));

        expect(context.onInteraction).toHaveBeenCalledWith({
          type: 'Phone Header Logo',
          value: 'Selected',
        });
      });
    });

    it('renders LoginTooltip', () => {
      renderComponent({ qffLoginEnabled: true });
      expect(screen.getByTestId('mock-login-tooltip')).toBeInTheDocument();
    });
  });

  describe('if qffLoginEnabled is false', () => {
    it('does NOT render LoginTooltip', () => {
      renderComponent({ qffLoginEnabled: false });
      expect(screen.queryByTestId('mock-login-tooltip')).not.toBeInTheDocument();
    });
  });

  it('renders a custom logo', () => {
    renderComponent({ LogoItemRenderer: Logo });
    expect(screen.getByTestId('custom-logo')).toHaveAttribute('src', 'qantas-hotels.svg');
  });

  describe('if navEnabled is false', () => {
    it('does NOT render PhoneMenu', () => {
      renderComponent({ navEnabled: false });
      expect(screen.queryByTestId('mock-phone-menu')).toBeNull();
    });

    it('does NOT render QffMenu', () => {
      renderComponent({ navEnabled: false });
      expect(screen.queryByTestId('mock-qff-menu')).not.toBeInTheDocument();
    });

    it('does NOT render logo link', () => {
      renderComponent({ navEnabled: false });
      expect(screen.queryByTestId('header-logo-link')).not.toBeInTheDocument();
    });

    it('renders LoginTooltip', () => {
      renderComponent({ navEnabled: false });
      expect(screen.getByTestId('mock-login-tooltip')).toBeInTheDocument();
    });
  });
});
