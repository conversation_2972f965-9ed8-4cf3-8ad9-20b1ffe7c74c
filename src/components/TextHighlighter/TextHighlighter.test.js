import React from 'react';
import { screen } from '@testing-library/react';
import { renderWithProviders } from '../../test-utils/reactUtils/renderWithProviders';
import TextHighlighter from './TextHighlighter';

describe('TextHighlighter', () => {
  const isBoldElement = (element) => {
    const computedStyle = window.getComputedStyle(element);
    const fontWeight = computedStyle.fontWeight;
    return fontWeight === 'bold' || fontWeight === '600' || fontWeight === '700';
  };

  const expectBoldStyle = (element) => {
    expect(element).toHaveStyle('font-weight: 600');
  };

  const expectNotBoldStyle = (element) => {
    expect(element).not.toHaveStyle('font-weight: 600');
  };

  const renderHighlighter = (text, highlightText) => {
    return renderWithProviders(<TextHighlighter text={text} highlightText={highlightText} />);
  };

  const expectEmptyContainer = (text, highlightText) => {
    const { container } = renderHighlighter(text, highlightText);
    expect(container.textContent).toBe('');
  };

  const expectBoldMatches = (text, highlightText, expectedCount) => {
    renderHighlighter(text, highlightText);
    const matchingElements = screen.getAllByText(highlightText);
    expect(matchingElements).toHaveLength(expectedCount);
    matchingElements.forEach(expectBoldStyle);
  };

  describe('basic functionality', () => {
    it('highlights text', () => {
      renderHighlighter('abc 123 abc', 'abc');

      const boldTexts = screen.getAllByText('abc');
      expect(boldTexts).toHaveLength(2);
      boldTexts.forEach(expectBoldStyle);

      expectNotBoldStyle(screen.getByText(/123/));
    });

    it('ignores case when matching text for highlighting', () => {
      renderHighlighter('AbC', 'abc');
      expectBoldStyle(screen.getByText('AbC'));
    });
  });

  describe('edge cases', () => {
    it.each([
      ['null text', null, 'abc'],
      ['undefined text', undefined, 'abc'],
      ['empty string text', '', 'abc'],
    ])('handles %s value', (_, text, highlightText) => {
      expectEmptyContainer(text, highlightText);
    });

    it.each([
      ['null highlight', 'abc 123', null],
      ['undefined highlight', 'abc 123', undefined],
      ['empty string highlight', 'abc 123', ''],
    ])('handles %s value', (_, text, highlightText) => {
      renderHighlighter(text, highlightText);
      expectNotBoldStyle(screen.getByText('abc 123'));
    });
  });

  describe('special characters', () => {
    it.each([
      ['single special character', 'abc( 123 abc', 'abc('],
      ['multiple special characters', '[abc]^$.*+?|()', '[abc]^$.*+?|()'],
    ])('handles %s in highlight text', (_, text, highlightText) => {
      renderHighlighter(text, highlightText);
      expectBoldStyle(screen.getByText(highlightText));
    });
  });

  describe('text positioning', () => {
    it('handles text that starts with highlight text', () => {
      renderHighlighter('hello world', 'hello');
      expectBoldStyle(screen.getByText('hello'));
      expectNotBoldStyle(screen.getByText(/world/));
    });

    it('handles text that ends with highlight text', () => {
      renderHighlighter('say hello', 'hello');
      expectNotBoldStyle(screen.getByText(/say/));
      expectBoldStyle(screen.getByText('hello'));
    });

    it('handles partial matches correctly', () => {
      renderHighlighter('abcdef', 'bcd');
      expectNotBoldStyle(screen.getByText('a'));
      expectBoldStyle(screen.getByText('bcd'));
      expectNotBoldStyle(screen.getByText('ef'));
    });
  });

  describe('multiple matches', () => {
    it('handles adjacent matches correctly', () => {
      renderHighlighter('abcabc', 'abc');
      const abcElements = screen.getAllByText(/abc/);
      expect(abcElements.length).toBeGreaterThan(0);
      const boldElements = abcElements.filter(isBoldElement);
      expect(boldElements.length).toBeGreaterThan(0);
    });

    it('handles overlapping case variations', () => {
      renderHighlighter('ABC abc AbC', 'abc');
      const matchingElements = screen.getAllByText(/abc/i);
      expect(matchingElements).toHaveLength(3);
      matchingElements.forEach(expectBoldStyle);
    });

    it('handles long text efficiently', () => {
      const longText =
        'This is a very long text that contains multiple instances of the word highlight. ' +
        'We want to make sure that highlight appears multiple times and is properly highlighted ' +
        'throughout this highlight test.';

      renderHighlighter(longText, 'highlight');
      const highlightElements = screen.getAllByText(/highlight/i);
      const boldHighlightElements = highlightElements.filter(isBoldElement);
      expect(boldHighlightElements).toHaveLength(4);
    });
  });

  describe('data types', () => {
    it.each([
      ['single character', 'a b a', 'a', 2],
      ['numeric text', '123 456 123', '123', 2],
      ['mixed alphanumeric', 'test123 other test123', 'test123', 2],
    ])('handles %s matching', (_, text, highlightText, expectedCount) => {
      expectBoldMatches(text, highlightText, expectedCount);
    });

    it('verifies non-matching elements are not bold', () => {
      renderHighlighter('a b a', 'a');
      expectNotBoldStyle(screen.getByText(/b/));
    });

    it('verifies non-matching numeric elements are not bold', () => {
      renderHighlighter('123 456 123', '123');
      expectNotBoldStyle(screen.getByText(/456/));
    });

    it('verifies non-matching alphanumeric elements are not bold', () => {
      renderHighlighter('test123 other test123', 'test123');
      expectNotBoldStyle(screen.getByText(/other/));
    });
  });
});
