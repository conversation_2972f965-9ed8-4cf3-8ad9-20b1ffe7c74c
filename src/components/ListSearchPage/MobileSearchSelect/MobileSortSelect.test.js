import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider } from 'emotion-theming';

import MobileSortSelect from './MobileSortSelect';
import { SORT_BY } from 'lib/enums/search';
import { updateQuery } from 'store/search/searchActions';
import theme from 'lib/theme';

const mockUseDispatch = jest.fn();
const mockUseSelector = jest.fn();

jest.mock('react-redux', () => ({
  useDispatch: () => mockUseDispatch,
  useSelector: (selector) => mockUseSelector(selector),
}));

jest.mock('store/router/routerSelectors');
jest.mock('store/search/searchActions', () => ({
  updateQuery: jest.fn((payload) => ({
    type: 'SEARCH/UPDATE_QUERY',
    payload,
  })),
}));

const renderComponent = () => {
  return render(
    <ThemeProvider theme={theme}>
      <MobileSortSelect />
    </ThemeProvider>,
  );
};

const defaultSortBy = SORT_BY.POPULARITY.code;

beforeEach(() => {
  jest.clearAllMocks();
  updateQuery.mockClear();
  mockUseDispatch.mockClear();
  mockUseSelector.mockImplementation((selector) => {
    if (selector.name === 'getQuerySortBy') {
      return defaultSortBy;
    }
    return undefined;
  });
});

it('renders the options', () => {
  renderComponent();

  expect(screen.getByRole('combobox')).toBeInTheDocument();

  const options = screen.getAllByRole('option');
  expect(options).toHaveLength(5);

  const promotionOption = screen.getByRole('option', { name: SORT_BY.PROMOTION.name });
  expect(promotionOption).toHaveValue(SORT_BY.PROMOTION.code);

  const popularityOption = screen.getByRole('option', { name: SORT_BY.POPULARITY.name });
  expect(popularityOption).toHaveValue(SORT_BY.POPULARITY.code);

  const rateDescOption = screen.getByRole('option', { name: SORT_BY.PRICE_DESC.name });
  expect(rateDescOption).toHaveValue(SORT_BY.PRICE_DESC.code);

  const rateAscOption = screen.getByRole('option', { name: SORT_BY.PRICE_ASC.name });
  expect(rateAscOption).toHaveValue(SORT_BY.PRICE_ASC.code);

  const minTripadvisorRatingOption = screen.getByRole('option', { name: SORT_BY.TRIPADVISOR.name });
  expect(minTripadvisorRatingOption).toHaveValue(SORT_BY.TRIPADVISOR.code);
});

it('updates the query params when a new option is selected', async () => {
  renderComponent();

  await userEvent.selectOptions(screen.getByRole('combobox'), 'price_asc');

  expect(updateQuery).toHaveBeenCalledTimes(1);
  expect(updateQuery).toHaveBeenCalledWith({ sortBy: 'price_asc' });
  expect(mockUseDispatch).toHaveBeenCalledWith(updateQuery({ sortBy: 'price_asc' }));
});

it('renders the filter copy', () => {
  renderComponent();

  expect(screen.getByTestId('mobile-filters-description')).toHaveTextContent('0 properties');
});
