/* eslint-disable no-unused-vars */
import React from 'react';
import { Decimal } from 'decimal.js';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { Provider } from 'react-redux';
import configureMockStore from 'redux-mock-store';
import checkVoucherBalance from 'lib/clients/checkVoucherBalance';
import checkIsVoucherPinRequired from 'lib/clients/checkIsVoucherPinRequired';
import { getTotalPayableAtBooking, getPropertyId } from 'store/quote/quoteSelectors';
import { getVoucherAmount } from 'store/checkout/checkoutSelectors';
import { updatePayments } from 'store/checkout/checkoutActions';
import { getVoucherErrors } from 'store/booking/bookingSelectors';
import HotelsVoucher, { GET_VOUCHER_BALANCE_FAILED_ERROR } from './HotelsVoucher';
import { useDataLayer } from 'hooks/useDataLayer';
import checkVoucher from 'lib/clients/checkVoucher';
import validateVoucher from './validateVoucher';
import { useViiVoucher } from './hooks/useViiVoucher';
import { useViiVoucherMaintenance } from './hooks/useViiVoucherMaintenance';

jest.mock('store/quote/quoteSelectors');
jest.mock('store/checkout/checkoutSelectors');
jest.mock('lib/clients/checkVoucherBalance');
jest.mock('lib/clients/checkIsVoucherPinRequired');
jest.mock('store/booking/bookingSelectors');
jest.mock('hooks/useDataLayer');
jest.mock('lib/clients/checkVoucher');
jest.mock('./validateVoucher');
jest.mock('./hooks/useViiVoucher');
jest.mock('./hooks/useViiVoucherMaintenance');

jest.mock('@qga/components', () => ({
  FieldError: ({ error }) => (error && error.message ? <div data-testid="field-error">{error.message}</div> : null),
  FieldLabel: ({ children, htmlFor }) => <label htmlFor={htmlFor}>{children}</label>,
}));

jest.mock('@qga/roo-ui/components', () => ({
  Box: ({ children, ...props }) => {
    const { justifyContent, alignItems, flexDirection, borderColor, borderBottom, borderRadius, ...domProps } = props;
    return <div {...domProps}>{children}</div>;
  },
  Flex: ({ children, ...props }) => {
    const { justifyContent, alignItems, flexDirection, borderColor, borderBottom, borderRadius, ...domProps } = props;
    return <div {...domProps}>{children}</div>;
  },
  Button: ({ children, ...props }) => <button {...props}>{children}</button>,
  Text: ({ children, ...props }) => <span {...props}>{children}</span>,
  Wrapper: ({ children, ...props }) => <div {...props}>{children}</div>,
  NakedButton: ({ children, ...props }) => <button {...props}>{children}</button>,
  Icon: ({ ...props }) => <svg {...props} />,
  Input: ({ error, initialValue, autocomplete, ...props }) => (
    <input {...props} defaultValue={initialValue} autoComplete={autocomplete} data-error={error ? 'true' : 'false'} />
  ),
  LoadingIndicator: ({ ...props }) => <div data-testid="loading-indicator" {...props} />,
}));

jest.mock('./VoucherTerms', () => {
  return function VoucherTerms() {
    return <div data-testid="voucher-terms">VoucherTerms</div>;
  };
});

jest.mock('components/CheckoutPage/BookingError', () => {
  return function BookingError({ errors, type }) {
    return <div data-testid="booking-error">{`${type}-${errors.length}`}</div>;
  };
});

jest.mock('components/Currency', () => {
  return function Currency({ amount, currency }) {
    return (
      <div data-testid="currency" data-amount={amount.toString()} data-currency={currency}>
        Currency
      </div>
    );
  };
});

jest.mock('components/CheckoutPage/ErrorAlert', () => {
  return function ErrorAlert({ children }) {
    return <div data-testid="error-alert">{children}</div>;
  };
});

jest.mock('components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/primitives', () => ({
  PaymentMethodSummaryButton: ({ children, ...props }) => <button {...props}>{children}</button>,
}));

beforeAll(() => {
  jest.spyOn(console, 'error').mockImplementation(() => {});
});

afterAll(() => {
  console.error.mockRestore();
});

const mockStore = configureMockStore([]);
const createTestStore = (state = {}) => mockStore(state);

const renderComponent = (store = createTestStore()) => {
  return render(
    <Provider store={store}>
      <HotelsVoucher />
    </Provider>,
  );
};

let totalPayableAtBooking = new Decimal(100);
const voucherAmount = new Decimal(0);
const voucherType = 'credit_voucher';
const voucherCode = '103260000001062998';
const pin = '1563';
const voucherErrors = [];
const emitInteractionEvent = jest.fn();
const propertyId = 101;

const simulateVoucherCodeChange = async (voucherCode) => {
  const addVoucherButton = screen.getByTestId('add-voucher-button');
  await userEvent.click(addVoucherButton);

  if (voucherCode) {
    const voucherCodeInput = screen.getByLabelText(/voucher code/i);
    await userEvent.type(voucherCodeInput, voucherCode);

    await new Promise((resolve) => setTimeout(resolve, 350));
  }
};

beforeEach(() => {
  getTotalPayableAtBooking.mockReturnValue(totalPayableAtBooking);
  getPropertyId.mockReturnValue(propertyId);
  getVoucherAmount.mockReturnValue(voucherAmount);
  getVoucherErrors.mockReturnValue(voucherErrors);
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  useViiVoucher.mockReturnValue({
    isReady: true,
    isViiVoucher: true,
  });
  useViiVoucherMaintenance.mockReturnValue({
    isReady: true,
    isViiVoucherMaintenance: false,
  });
  checkIsVoucherPinRequired.mockImplementation(() =>
    Promise.resolve({
      pinRequired: false,
    }),
  );
  checkVoucherBalance.mockImplementation(() => Promise.resolve({ availableBalance: new Decimal(0) }));
  checkVoucher.mockImplementation(() => Promise.resolve({ amount: new Decimal(0) }));
  validateVoucher.mockReturnValue('');
});

afterEach(() => {
  jest.resetAllMocks();
});

it('renders the ADD VOUCHER button', async () => {
  renderComponent();
  expect(screen.getByTestId('add-voucher-button')).toHaveTextContent('ADD');
});

it('disables the ADD VOUCHER button when voucher maintenance feature flag is on', async () => {
  useViiVoucherMaintenance.mockReturnValue({
    isReady: true,
    isViiVoucherMaintenance: true,
  });
  renderComponent();
  expect(screen.getByTestId('add-voucher-button')).toBeDisabled();
});

describe('with an initial voucher amount greater than zero', () => {
  let voucherAmount = new Decimal(100);

  beforeEach(() => {
    getVoucherAmount.mockReturnValue(voucherAmount);
  });

  it('renders the voucher amount', async () => {
    renderComponent();
    expect(screen.getByTestId('currency')).toHaveAttribute('data-amount', voucherAmount.toString());
    expect(screen.getByTestId('currency')).toHaveAttribute('data-currency', 'AUD');
  });
});

describe('with a voucher error caused by booking submission', () => {
  const voucherErrors = [{ code: 'payment_failed_voucher', message: 'message' }];

  beforeEach(() => {
    getVoucherErrors.mockReturnValue(voucherErrors);
  });

  it('renders the BookingError with voucher errors', async () => {
    renderComponent();
    expect(screen.getByTestId('booking-error')).toHaveTextContent('voucher-1');
  });

  it('clears the voucher code and amount', async () => {
    const store = createTestStore();
    renderComponent(store);

    expect(store.getActions()).toContainEqual(updatePayments({ voucher: { type: null, code: null, amount: new Decimal(0), pin: null } }));
  });
});

describe('adding a voucher', () => {
  let voucherAmount = new Decimal(100);

  it('renders the voucher terms link', async () => {
    renderComponent();

    const addVoucherButton = screen.getByTestId('add-voucher-button');

    await userEvent.click(addVoucherButton);

    await waitFor(() => {
      expect(screen.getByTestId('voucher-terms')).toBeInTheDocument();
    });
  });

  describe('when vii voucher feature flag is off', () => {
    beforeEach(() => {
      useViiVoucher.mockReturnValue({
        isReady: true,
        isViiVoucher: false,
      });
    });

    it('renders the apply button as disabled', async () => {
      renderComponent();

      const addVoucherButton = screen.getByTestId('add-voucher-button');
      await userEvent.click(addVoucherButton);

      expect(screen.getByTestId('apply-voucher-button')).toBeDisabled();
    });

    it('enables the apply button after entering a voucher code', async () => {
      renderComponent();

      await simulateVoucherCodeChange(voucherCode);

      expect(screen.getByTestId('apply-voucher-button')).toBeEnabled();
    });

    it('does not check if voucher pin is required', async () => {
      renderComponent();

      await simulateVoucherCodeChange(voucherCode);

      expect(checkIsVoucherPinRequired).not.toHaveBeenCalled();
    });

    it('checks the voucher code when clicking apply', async () => {
      checkVoucher.mockReturnValue(Promise.resolve({ amount: voucherAmount }));
      renderComponent();

      await simulateVoucherCodeChange(voucherCode);

      const applyButton = screen.getByTestId('apply-voucher-button');
      await userEvent.click(applyButton);

      await waitFor(() => {
        expect(checkVoucher).toHaveBeenCalledWith({ voucherCode, totalPayableAtBooking, propertyId });
      });
    });

    it('updates form data with returned voucher amount and code', async () => {
      checkVoucher.mockReturnValue(Promise.resolve({ amount: voucherAmount }));
      validateVoucher.mockReturnValue(voucherCode);

      const store = createTestStore();
      renderComponent(store);

      await simulateVoucherCodeChange(voucherCode);

      const applyButton = screen.getByTestId('apply-voucher-button');
      await userEvent.click(applyButton);

      await waitFor(() => {
        expect(validateVoucher).toHaveBeenCalledWith(voucherCode);
      });
      await waitFor(() => {
        expect(store.getActions()).toContainEqual(updatePayments({ voucher: { amount: voucherAmount, code: voucherCode } }));
      });
    });
  });

  it('checks if voucher pin is required after entering more than 4 characters', async () => {
    renderComponent();

    await simulateVoucherCodeChange(voucherCode);

    await waitFor(() => {
      expect(checkIsVoucherPinRequired).toHaveBeenCalledWith({ voucherCode });
    });
  });

  it('enables the apply button after entering a voucher code if voucher pin check is finished and pin is not required', async () => {
    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: false,
    });

    renderComponent();

    await simulateVoucherCodeChange(voucherCode);

    await waitFor(() => {
      expect(screen.getByTestId('apply-voucher-button')).toBeEnabled();
    });
  });

  it('enables the apply button after entering a voucher code and pin if voucher pin check is finished and pin is required', async () => {
    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: true,
    });

    renderComponent();

    await simulateVoucherCodeChange(voucherCode);

    await waitFor(() => {
      expect(screen.getByLabelText(/pin/i)).toBeInTheDocument();
    });

    const pinInput = screen.getByLabelText(/pin/i);
    await userEvent.type(pinInput, pin);

    expect(screen.getByTestId('apply-voucher-button')).toBeEnabled();
  });

  it('displays voucher pin check error', async () => {
    const errorMessage = 'errorMessage';

    checkIsVoucherPinRequired.mockRejectedValue(
      new Error({
        response: {
          data: {
            errorMessage,
          },
        },
      }),
    );

    renderComponent();

    await simulateVoucherCodeChange(voucherCode);

    expect(checkIsVoucherPinRequired).toHaveBeenCalledWith({ voucherCode });

    await waitFor(() => {
      expect(screen.getByText(GET_VOUCHER_BALANCE_FAILED_ERROR)).toBeInTheDocument();
    });
  });

  it("displays default voucher pin check error if the response doesn't include any error messages", async () => {
    checkIsVoucherPinRequired.mockRejectedValue(new Error("Can't use this voucher"));

    renderComponent();

    await simulateVoucherCodeChange(voucherCode);

    expect(checkIsVoucherPinRequired).toHaveBeenCalledWith({ voucherCode });

    await waitFor(() => {
      expect(screen.getByText(GET_VOUCHER_BALANCE_FAILED_ERROR)).toBeInTheDocument();
    });
  });

  it('calls voucher pin check again after voucher code change if there is an error', async () => {
    checkIsVoucherPinRequired.mockRejectedValue(new Error("Can't use this voucher"));

    renderComponent();

    await simulateVoucherCodeChange(voucherCode);

    expect(checkIsVoucherPinRequired).toHaveBeenCalledWith({ voucherCode });

    await waitFor(() => {
      expect(screen.getByText(GET_VOUCHER_BALANCE_FAILED_ERROR)).toBeInTheDocument();
    });

    const newVoucherCode = 'new voucher code';
    const voucherCodeInput = screen.getByLabelText(/voucher code/i);
    await userEvent.clear(voucherCodeInput);
    await userEvent.type(voucherCodeInput, newVoucherCode);

    await new Promise((resolve) => setTimeout(resolve, 350));

    expect(checkIsVoucherPinRequired).toHaveBeenCalledWith({ voucherCode: newVoucherCode });
  });

  it('displays pin input if voucher pin is required', async () => {
    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: true,
    });

    renderComponent();

    await simulateVoucherCodeChange(voucherCode);

    await waitFor(() => {
      expect(screen.getByLabelText(/pin/i)).toBeInTheDocument();
    });
  });

  it('checks the voucher code amount when clicking apply', async () => {
    checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount }));
    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: false,
    });

    renderComponent();

    await simulateVoucherCodeChange(voucherCode);

    await waitFor(() => {
      expect(screen.getByTestId('apply-voucher-button')).toBeEnabled();
    });

    const applyButton = screen.getByTestId('apply-voucher-button');
    await userEvent.click(applyButton);

    await waitFor(() => {
      expect(checkVoucherBalance).toHaveBeenCalledWith({ voucherCode, pin: '' });
    });
  });

  it('validates form, displays errors and does not check voucher balance if voucher code is not provided', async () => {
    checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount, voucherPaymentType: voucherType }));
    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: true,
    });

    renderComponent();

    await simulateVoucherCodeChange('');

    await waitFor(() => {
      expect(screen.getByTestId('apply-voucher-button')).toBeInTheDocument();
    });

    const applyButton = screen.getByTestId('apply-voucher-button');
    await userEvent.click(applyButton);

    await waitFor(() => {
      expect(checkVoucherBalance).not.toHaveBeenCalled();
    });
    await waitFor(() => {
      expect(screen.getByText('Voucher code is required')).toBeInTheDocument();
    });
  });

  it('validates form, displays errors and does not check voucher balance if pin is not provided', async () => {
    checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount, voucherPaymentType: voucherType }));
    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: true,
    });

    renderComponent();

    await simulateVoucherCodeChange('103260000001063219');

    await waitFor(() => {
      expect(screen.getByTestId('apply-voucher-button')).toBeInTheDocument();
    });

    const applyButton = screen.getByTestId('apply-voucher-button');
    await userEvent.click(applyButton);

    await waitFor(() => {
      expect(checkVoucherBalance).not.toHaveBeenCalled();
    });
    await waitFor(() => {
      expect(screen.getByText('PIN is required')).toBeInTheDocument();
    });
  });

  it('validates form, displays errors and does not check voucher balance if voucher code or pin are incorrect', async () => {
    checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount, voucherPaymentType: voucherType }));
    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: true,
    });

    renderComponent();

    await simulateVoucherCodeChange('ABC1235456444556454Q#');

    await waitFor(() => {
      expect(screen.getByLabelText(/pin/i)).toBeInTheDocument();
    });

    const pinInput = screen.getByLabelText(/pin/i);
    await userEvent.type(pinInput, 'A99');

    const applyButton = screen.getByTestId('apply-voucher-button');
    await userEvent.click(applyButton);

    await waitFor(() => {
      expect(checkVoucherBalance).not.toHaveBeenCalled();
    });
    await waitFor(() => {
      expect(screen.getAllByText('Voucher code is incorrect')).toHaveLength(1);
    });
    await waitFor(() => {
      expect(screen.getAllByText('PIN is incorrect')).toHaveLength(1);
    });
  });

  it('checks the voucher code amount with voucher code and pin when clicking apply', async () => {
    checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount, voucherPaymentType: voucherType }));
    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: true,
    });

    renderComponent();

    await simulateVoucherCodeChange(voucherCode);

    await waitFor(() => {
      expect(screen.getByLabelText(/pin/i)).toBeInTheDocument();
    });

    const pinInput = screen.getByLabelText(/pin/i);
    await userEvent.type(pinInput, pin);

    const applyButton = screen.getByTestId('apply-voucher-button');
    await userEvent.click(applyButton);

    await waitFor(() => {
      expect(checkVoucherBalance).toHaveBeenCalledWith({ voucherCode, pin });
    });
  });

  it('dispatches an event to the data layer', async () => {
    checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount }));
    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: false,
    });

    renderComponent();

    await simulateVoucherCodeChange(voucherCode);

    await waitFor(() => {
      expect(screen.getByTestId('apply-voucher-button')).toBeEnabled();
    });

    const applyButton = screen.getByTestId('apply-voucher-button');
    await userEvent.click(applyButton);

    await waitFor(() => {
      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Voucher Link',
        value: 'Voucher Succeeded',
      });
    });
  });

  it('updates form data with returned voucher amount and code', async () => {
    checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount, voucherPaymentType: voucherType }));
    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: false,
    });

    const store = createTestStore();
    renderComponent(store);

    await simulateVoucherCodeChange(voucherCode);

    await waitFor(() => {
      expect(screen.getByTestId('apply-voucher-button')).toBeEnabled();
    });

    const applyButton = screen.getByTestId('apply-voucher-button');
    await userEvent.click(applyButton);

    await waitFor(() => {
      expect(store.getActions()).toContainEqual(
        updatePayments({ voucher: { type: voucherType, code: voucherCode, amount: voucherAmount } }),
      );
    });
  });

  it('updates form data with returned voucher amount, code and pin', async () => {
    checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount, voucherPaymentType: voucherType }));
    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: true,
    });

    const store = createTestStore();
    renderComponent(store);

    await simulateVoucherCodeChange(voucherCode);

    await waitFor(() => {
      expect(screen.getByLabelText(/pin/i)).toBeInTheDocument();
    });

    const pinInput = screen.getByLabelText(/pin/i);
    await userEvent.type(pinInput, pin);

    const applyButton = screen.getByTestId('apply-voucher-button');
    await userEvent.click(applyButton);

    await waitFor(() => {
      expect(store.getActions()).toContainEqual(
        updatePayments({ voucher: { type: voucherType, code: voucherCode, amount: voucherAmount, pin } }),
      );
    });
  });

  describe('with a voucher amount that is more than the booking value', () => {
    it('allocates the voucher amount equal to the booking total', async () => {
      totalPayableAtBooking = voucherAmount.minus(10);
      getTotalPayableAtBooking.mockReturnValue(totalPayableAtBooking);
      checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount, voucherPaymentType: voucherType }));
      checkIsVoucherPinRequired.mockReturnValue({
        pinRequired: false,
      });

      const store = createTestStore();
      renderComponent(store);

      await simulateVoucherCodeChange(voucherCode);

      await waitFor(() => {
        expect(screen.getByTestId('apply-voucher-button')).toBeEnabled();
      });

      const applyButton = screen.getByTestId('apply-voucher-button');
      await userEvent.click(applyButton);

      await waitFor(() => {
        expect(store.getActions()).toContainEqual(
          updatePayments({ voucher: { type: voucherType, code: voucherCode, amount: totalPayableAtBooking } }),
        );
      });
    });
  });

  describe('cancelling an edit', () => {
    it('hides the form', async () => {
      renderComponent();

      const addVoucherButton = screen.getByTestId('add-voucher-button');
      await userEvent.click(addVoucherButton);

      expect(screen.getByLabelText(/voucher code/i)).toBeInTheDocument();

      const cancelButton = screen.getByTestId('cancel-edit-voucher-button');
      await userEvent.click(cancelButton);

      expect(screen.queryByLabelText(/voucher code/i)).not.toBeInTheDocument();
    });
  });

  describe('removing the voucher', () => {
    it('displays the ADD button', async () => {
      checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount }));
      checkIsVoucherPinRequired.mockReturnValue({
        pinRequired: false,
      });

      renderComponent();

      await simulateVoucherCodeChange(voucherCode);

      await waitFor(() => {
        expect(screen.getByTestId('apply-voucher-button')).toBeEnabled();
      });

      const applyButton = screen.getByTestId('apply-voucher-button');
      await userEvent.click(applyButton);

      await waitFor(() => {
        expect(screen.getByTestId('remove-voucher-button')).toBeInTheDocument();
      });

      const removeButton = screen.getByTestId('remove-voucher-button');
      await userEvent.click(removeButton);

      expect(screen.getByTestId('add-voucher-button')).toHaveTextContent('ADD');
    });

    it('clears the voucher formData', async () => {
      checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount, voucherPaymentType: voucherType }));
      checkIsVoucherPinRequired.mockReturnValue({
        pinRequired: false,
      });

      const store = createTestStore();
      renderComponent(store);

      await simulateVoucherCodeChange(voucherCode);

      await waitFor(() => {
        expect(screen.getByTestId('apply-voucher-button')).toBeEnabled();
      });

      const applyButton = screen.getByTestId('apply-voucher-button');
      await userEvent.click(applyButton);

      await waitFor(() => {
        expect(screen.getByTestId('remove-voucher-button')).toBeInTheDocument();
      });

      const removeButton = screen.getByTestId('remove-voucher-button');
      await userEvent.click(removeButton);

      expect(store.getActions()).toContainEqual(updatePayments({ voucher: { type: null, code: null, amount: new Decimal(0), pin: null } }));
    });
  });

  describe('with an invalid voucher code', () => {
    const fillInvalidVoucherAndApply = async () => {
      checkVoucherBalance.mockRejectedValue({ response: { data: { errorMessage: 'Invalid voucher code' } } });
      checkIsVoucherPinRequired.mockReturnValue({
        pinRequired: false,
      });

      renderComponent();

      await simulateVoucherCodeChange(voucherCode);

      await waitFor(() => {
        expect(screen.getByTestId('apply-voucher-button')).toBeEnabled();
      });

      const applyButton = screen.getByTestId('apply-voucher-button');
      await userEvent.click(applyButton);

      await waitFor(() => {
        expect(screen.getByText('Invalid voucher code')).toBeInTheDocument();
      });
    };

    it("displays a default error if the response doesn't include any error messages", async () => {
      await fillInvalidVoucherAndApply();
      expect(screen.getByText('Invalid voucher code')).toBeInTheDocument();
    });

    it('displays the response error message if the response includes one', async () => {
      const error = new Error();
      error.response = {
        data: {
          errorMessage: "Can't use this voucher",
        },
      };

      checkVoucherBalance.mockRejectedValue(error);
      checkIsVoucherPinRequired.mockReturnValue({
        pinRequired: false,
      });

      renderComponent();

      await simulateVoucherCodeChange(voucherCode);

      await waitFor(() => {
        expect(screen.getByTestId('apply-voucher-button')).toBeEnabled();
      });

      const applyButton = screen.getByTestId('apply-voucher-button');
      await userEvent.click(applyButton);

      await waitFor(() => {
        expect(screen.getByText("Can't use this voucher")).toBeInTheDocument();
      });
    });

    it('dispatches an event to the data layer', async () => {
      await fillInvalidVoucherAndApply();

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Voucher Link',
        value: 'Voucher Invalid',
      });
    });

    it('clears the error when changing the voucher code', async () => {
      await fillInvalidVoucherAndApply();

      const voucherCodeInput = screen.getByLabelText(/voucher code/i);
      await userEvent.type(voucherCodeInput, 'new voucher code');

      await new Promise((resolve) => setTimeout(resolve, 350));

      await waitFor(() => {
        expect(screen.queryByText('Invalid voucher code')).not.toBeInTheDocument();
      });
    });
  });
});
