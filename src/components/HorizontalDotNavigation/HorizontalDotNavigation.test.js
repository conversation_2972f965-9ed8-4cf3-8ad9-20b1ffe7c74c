import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ThemeProvider } from '@qga/roo-ui';

import HorizontalDotNavigation from '../HorizontalDotNavigation';

const mockTheme = {
  colors: {
    greys: {
      dusty: '#9B9B9B',
      alto: '#D8D8D8',
    },
  },
};

describe('HorizontalDotNavigation', () => {
  it('renders the correct number of dots and highlights the correct one', () => {
    const totalDots = 5;
    const currentPosition = 2;
    render(
      <ThemeProvider theme={mockTheme}>
        <HorizontalDotNavigation total={totalDots} position={currentPosition} />
      </ThemeProvider>,
    );

    const dots = screen.getAllByRole('listitem');
    expect(dots).toHaveLength(totalDots);

    const currentDot = screen.getByRole('listitem', { current: true });
    expect(currentDot).toBeInTheDocument();

    const currentDots = screen.getAllByRole('listitem', { current: true });
    expect(currentDots).toHaveLength(1);
  });

  it('renders with default props when no props are provided', () => {
    render(
      <ThemeProvider theme={mockTheme}>
        <HorizontalDotNavigation />
      </ThemeProvider>,
    );

    const dots = screen.getAllByRole('listitem');
    expect(dots).toHaveLength(3);

    const currentDot = screen.getByRole('listitem', { current: true });
    expect(currentDot).toBeInTheDocument();

    const currentDots = screen.getAllByRole('listitem', { current: true });
    expect(currentDots).toHaveLength(1);
  });

  it('renders correctly when position is 0', () => {
    render(
      <ThemeProvider theme={mockTheme}>
        <HorizontalDotNavigation total={3} position={0} />
      </ThemeProvider>,
    );

    const dots = screen.getAllByRole('listitem');
    expect(dots).toHaveLength(3);

    const currentDot = screen.getByRole('listitem', { current: true });
    expect(currentDot).toBeInTheDocument();
  });

  it('renders correctly when position is the last dot', () => {
    render(
      <ThemeProvider theme={mockTheme}>
        <HorizontalDotNavigation total={4} position={3} />
      </ThemeProvider>,
    );

    const dots = screen.getAllByRole('listitem');
    expect(dots).toHaveLength(4);

    const currentDot = screen.getByRole('listitem', { current: true });
    expect(currentDot).toBeInTheDocument();
  });

  it('applies the correct size to the dots', () => {
    const customSize = 20;
    render(
      <ThemeProvider theme={mockTheme}>
        <HorizontalDotNavigation total={1} size={customSize} />
      </ThemeProvider>,
    );
    const dot = screen.getByRole('listitem');
    const computedStyle = window.getComputedStyle(dot);
    expect(computedStyle.width).toBe(`${customSize}px`);
    expect(computedStyle.height).toBe(`${customSize}px`);
  });

  it('does not render any dots when total is 0', () => {
    render(
      <ThemeProvider theme={mockTheme}>
        <HorizontalDotNavigation total={0} />
      </ThemeProvider>,
    );
    const dots = screen.queryAllByRole('listitem');
    expect(dots).toHaveLength(0);
  });

  it('handles negative position prop gracefully (should highlight no dot)', () => {
    render(
      <ThemeProvider theme={mockTheme}>
        <HorizontalDotNavigation total={3} position={-1} />
      </ThemeProvider>,
    );
    const dots = screen.getAllByRole('listitem');
    expect(dots).toHaveLength(3);

    const currentDots = screen.queryAllByRole('listitem', { current: true });
    expect(currentDots).toHaveLength(0);
  });

  it('handles position greater than total gracefully (should highlight no dot)', () => {
    render(
      <ThemeProvider theme={mockTheme}>
        <HorizontalDotNavigation total={3} position={5} />
      </ThemeProvider>,
    );
    const dots = screen.getAllByRole('listitem');
    expect(dots).toHaveLength(3);

    const currentDots = screen.queryAllByRole('listitem', { current: true });
    expect(currentDots).toHaveLength(0);
  });
});
