import React, { FC } from 'react';
import { Box, Flex } from '@qga/roo-ui/components';
import PulsingLoadingBlock from 'components/PulsingLoadingBlock';

interface StoredPaymentLoaderSkeletonProps {
  numberOfItems: number;
}

const StoredPaymentLoaderSkeleton: FC<StoredPaymentLoaderSkeletonProps> = ({ numberOfItems }) => (
  <Box
    data-testid="stored-payment-loader-skeleton"
    padding="4"
    height={`${String((numberOfItems > 1 ? 376 : 368) + numberOfItems * 86)}px`}
  >
    <Flex flexDirection="column" mb="2">
      <PulsingLoadingBlock height="25px" width={['40%', '25%']} />
    </Flex>
    <Flex flexDirection="column" gap="9px">
      <PulsingLoadingBlock borderRadius="8px" height="244px" width="100%" />
      {Array.from({ length: numberOfItems - 1 }, (_, index) => (
        <PulsingLoadingBlock data-testid="stored-payment-loader" key={index} borderRadius="8px" height="69px" width="100%" />
      ))}
    </Flex>
    <Flex flexDirection="column" mb="2">
      <PulsingLoadingBlock mt="6" mb="4" height="25px" width={['40%', '25%']} />
      <PulsingLoadingBlock data-testid="stored-payment-loader" borderRadius="8px" height="69px" width="100%" />
    </Flex>
  </Box>
);

export default StoredPaymentLoaderSkeleton;
