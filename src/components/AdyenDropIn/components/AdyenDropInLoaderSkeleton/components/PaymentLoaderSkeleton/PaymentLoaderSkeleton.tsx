import React from 'react';
import { Box, Flex } from '@qga/roo-ui/components';
import PulsingLoadingBlock from 'components/PulsingLoadingBlock';

const borderRadius = 0;
const labelWidth = 70;
const labelHeight = 20;
const inputHeight = 60;

const PaymentLoaderSkeleton = () => (
  <Box data-testid="payment-loader-skeleton" padding="4" height="454px">
    <Flex flexDirection="column" mb="2" gap="2">
      <PulsingLoadingBlock height={22} width={['60%', '35%']} />
      <PulsingLoadingBlock height={15} width={['40%', '25%']} />
    </Flex>
    <Flex flexDirection="column" mb={[5, 10]} gap="2">
      <PulsingLoadingBlock mb="0" height={labelHeight} width={labelWidth} />
      <PulsingLoadingBlock borderRadius={borderRadius} mb={[6, 0]} height={inputHeight} width="100%" />
    </Flex>
    <Flex flexDirection="row" alignContent="space-between" alignItems="center" mb={[4, 10]} gap="3">
      <Flex flexDirection="column" width="100%" gap="2">
        <PulsingLoadingBlock mb="0" height={labelHeight} width={labelWidth} />
        <PulsingLoadingBlock borderRadius={borderRadius} mb={[6, 0]} height={inputHeight} width="100%" />
      </Flex>
      <Flex flexDirection="column" width="100%" gap="2">
        <PulsingLoadingBlock mb="0" height={labelHeight} width={labelWidth} />
        <PulsingLoadingBlock borderRadius={borderRadius} mb={[6, 0]} height={inputHeight} width="100%" />
      </Flex>
    </Flex>
    <Flex flexDirection="column" gap="2">
      <PulsingLoadingBlock mb="0" height={labelHeight} width={labelWidth} />
      <PulsingLoadingBlock borderRadius={borderRadius} mb={[6, 0]} height={inputHeight} width="100%" />
    </Flex>
  </Box>
);

export default PaymentLoaderSkeleton;
