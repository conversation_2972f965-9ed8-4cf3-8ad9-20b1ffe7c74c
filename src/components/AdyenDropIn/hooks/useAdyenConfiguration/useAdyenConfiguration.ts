import { useMemo, useState } from 'react';
import { AdyenCheckoutError, CoreConfiguration } from 'adyen-web-v6';
import { useSelector } from 'react-redux';
import { EnvironmentType } from 'types/config';
import { ADYEN_ENVIRONMENT, ADYEN_QEPG_CLIENT_KEY, IS_PRODUCTION } from 'config';
import { getPayableNowCashAmount, getPayableLaterCashAmount } from 'store/checkout/checkoutSelectors';
import { getPayableLaterDueDate } from 'store/quote/quoteSelectors';
import getTotal from './utils/getTotal';
import getChargesDescription from './utils/getChargesDescription';
import { PaymentMethod, StoredPaymentMethod } from 'lib/clients/getPaymentMethods';

export const DEFAULT_PAYMENT_METHODS = [
  {
    type: 'scheme',
    name: 'Credit Card',
    brands: ['visa', 'mc', 'amex'],
  },
];

interface AdyenConfiguration extends CoreConfiguration {
  onError: (error: AdyenCheckoutError) => void;
}

interface Config {
  paymentMethods?: PaymentMethod[];
  storedPaymentMethods?: StoredPaymentMethod[];
}

interface Result {
  adyenConfiguration: AdyenConfiguration;
  error?: string;
}

const useAdyenConfiguration = (config?: Config): Result => {
  const payableNowCashAmount = useSelector(getPayableNowCashAmount);
  const payableLaterCashAmount = useSelector(getPayableLaterCashAmount);
  const payableLaterDueDate = useSelector(getPayableLaterDueDate);
  const [error, setError] = useState<string>();

  const adyenConfiguration = useMemo(
    () => ({
      environment: ADYEN_ENVIRONMENT as EnvironmentType,
      clientKey: ADYEN_QEPG_CLIENT_KEY,
      countryCode: 'AU',
      locale: 'en-US',
      showPayButton: false,
      analytics: {
        enabled: IS_PRODUCTION,
      },
      paymentMethodsResponse: {
        paymentMethods: config?.paymentMethods || DEFAULT_PAYMENT_METHODS,
        storedPaymentMethods: config?.storedPaymentMethods || [],
      },
      amount: getTotal(payableNowCashAmount),
      translations: {
        'en-US': {
          'form.instruction': getChargesDescription(payableNowCashAmount, payableLaterCashAmount, payableLaterDueDate),
          'creditCard.expiryDate.contextualText': 'MM/YY format',
          'creditCard.securityCode.contextualText.3digits': '3 digits CVC/CVV2',
          'creditCard.securityCode.contextualText.4digits': '4 digits CID',
          'paymentMethodsList.storedPayments.label': 'Saved payment methods',
          'paymentMethodsList.otherPayments.label': 'Other payment methods',
          storeDetails:
            'Save these card details for future bookings. I confirm I am the cardholder, or I have express permission from the cardholder to save and use this card. If the cardholder withdraws permission, I will remove the card from my membership profile',
        },
      },
      onError: ({ message }) => setError(message),
      onEnterKeyPressed: (activeElement: HTMLElement) => {
        const formElement = activeElement.closest('input');

        if (formElement) {
          formElement.addEventListener('submit', (e) => e.preventDefault(), { once: true });
        }
      },
    }),
    [config, payableLaterCashAmount, payableLaterDueDate, payableNowCashAmount],
  );

  return {
    adyenConfiguration,
    error,
  };
};

export default useAdyenConfiguration;
