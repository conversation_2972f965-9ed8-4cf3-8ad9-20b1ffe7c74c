import { act, renderHook } from '@testing-library/react-hooks';
import { Card, CardConfigSuccessData, CardFieldValidData } from 'adyen-web-v6';
import useDropInConfiguration from './useDropInConfiguration';
import { FIELD_TYPE_ENCRYPTED_CARD_NUMBER, PAYMENT_TYPE_CARD } from 'config';

jest.mock('emotion-theming', () => ({
  ...jest.requireActual('emotion-theming'), // keep other exports working
  useTheme: jest.fn(() => ({
    fontFamily: 'Ciutadella, sans-serif',
  })),
}));

afterEach(() => {
  jest.resetAllMocks();
});

const qepgVault = {
  memberId: '**********',
  accountType: 'QFF',
};

it('returns dropInConfiguration default state', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current).toEqual({
    dropInConfiguration: {
      paymentMethodComponents: [Card],
      openFirstPaymentMethod: true,
      disableFinalAnimation: true,
      paymentMethodsConfiguration: {
        card: {
          name: 'Enter your credit or debit card details',
          hasHolderName: true,
          holderNameRequired: true,
          hideCVC: false,
          billingAddressRequired: false,
          onConfigSuccess: expect.any(Function),
          onBinValue: expect.any(Function),
          onFieldValid: expect.any(Function),
          styles: {
            base: {
              fontFamily: 'Ciutadella, sans-serif',
            },
          },
          enableStoreDetails: false,
        },
        storedCard: {
          onConfigSuccess: expect.any(Function),
          onBinValue: expect.any(Function),
          onFieldValid: expect.any(Function),
          styles: {
            base: {
              fontFamily: 'Ciutadella, sans-serif',
            },
          },
        },
      },
      onReady: expect.any(Function),
    },
    isDropInReady: false,
    isLoading: true,
  });
});

it('returns updated form title when qepgVault and stored payment details are available', () => {
  const { result } = renderHook(() => useDropInConfiguration({ qepgVault, isStoredPaymentAvailable: true }));

  expect(result.current?.dropInConfiguration?.paymentMethodsConfiguration?.card?.name).toEqual('Enter new credit or debit card details');
});

it('returns enableStoreDetails flag as true if it is enabled in config', () => {
  const { result } = renderHook(() => useDropInConfiguration({ qepgVault }));

  expect(result.current?.dropInConfiguration?.paymentMethodsConfiguration?.card?.enableStoreDetails).toBeTruthy();
});

it('calls onConfigSuccess with iframe being configured and returns isLoading state as false', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.isLoading).toBe(true);

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.card?.onConfigSuccess?.({
      iframesConfigured: true,
    } as CardConfigSuccessData);
  });

  expect(result.current.isLoading).toBe(false);
});

it('calls onConfigSuccess with iframe not being configured and returns isLoading state as true', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.isLoading).toBe(true);

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.card?.onConfigSuccess?.({
      iframesConfigured: false,
    } as CardConfigSuccessData);
  });

  expect(result.current.isLoading).toBe(true);
});

it('calls onBinValue with encrypted bin value and calls onFieldValid with valid encryptedCardNumber field and returns bin state', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.creditCardIdentifiers).toBeUndefined();

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.card?.onBinValue?.({
      type: PAYMENT_TYPE_CARD,
      binValue: '443573',
      encryptedBin: 'encrypted-bin-value',
    });
  });

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.card?.onFieldValid?.({
      valid: true,
      type: PAYMENT_TYPE_CARD,
      fieldType: FIELD_TYPE_ENCRYPTED_CARD_NUMBER,
      endDigits: '7890',
    } as CardFieldValidData);
  });

  expect(result.current.creditCardIdentifiers).toEqual({
    bin: '443573',
    tokenizedCreditCard: '443573xxxxxx7890',
  });
});

it('calls onBinValue with encrypted bin value and returns bin state as null', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.creditCardIdentifiers).toBeUndefined();

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.card?.onBinValue?.({
      type: PAYMENT_TYPE_CARD,
      binValue: '443573',
      encryptedBin: 'encrypted-bin-value',
    });
  });

  expect(result.current.creditCardIdentifiers).toBeUndefined();
});

it('calls onBinValue with invalid card type and returns bin state as null', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.creditCardIdentifiers).toBeUndefined();

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.card?.onBinValue?.({
      type: 'apple-pay',
      binValue: 'bin-value',
      encryptedBin: 'encrypted-bin-value',
    });
  });

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.card?.onFieldValid?.({
      valid: true,
      type: PAYMENT_TYPE_CARD,
      fieldType: FIELD_TYPE_ENCRYPTED_CARD_NUMBER,
      endDigits: '7890',
    } as CardFieldValidData);
  });

  expect(result.current.creditCardIdentifiers).toBeUndefined();
});

it('calls onBinValue with valid card type but no encryptedBin and returns bin state as null', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.creditCardIdentifiers).toBeUndefined();

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.card?.onBinValue?.({
      type: 'apple-pay',
      binValue: 'bin-value',
    });
  });

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.card?.onFieldValid?.({
      valid: true,
      type: PAYMENT_TYPE_CARD,
      fieldType: FIELD_TYPE_ENCRYPTED_CARD_NUMBER,
      endDigits: '7890',
    } as CardFieldValidData);
  });

  expect(result.current.creditCardIdentifiers).toBeUndefined();
});

it('calls onFieldValid with valid encryptedCardNumber field and returns bin state as null', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.creditCardIdentifiers).toBeUndefined();

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.card?.onFieldValid?.({
      valid: true,
      type: PAYMENT_TYPE_CARD,
      fieldType: FIELD_TYPE_ENCRYPTED_CARD_NUMBER,
      endDigits: '7890',
    } as CardFieldValidData);
  });

  expect(result.current.creditCardIdentifiers).toBeUndefined();
});

it('calls onFieldValid with invalid card type and returns bin state', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.creditCardIdentifiers).toBeUndefined();

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.card?.onBinValue?.({
      type: PAYMENT_TYPE_CARD,
      binValue: '443573',
      encryptedBin: 'encrypted-bin-value',
    });
  });

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.card?.onFieldValid?.({
      valid: true,
      type: 'apple-pay',
      fieldType: FIELD_TYPE_ENCRYPTED_CARD_NUMBER,
      endDigits: '7890',
    } as CardFieldValidData);
  });

  expect(result.current.creditCardIdentifiers).toBeUndefined();
});

it('calls onFieldValid with invalid field type and returns bin state', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.creditCardIdentifiers).toBeUndefined();

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.card?.onBinValue?.({
      type: PAYMENT_TYPE_CARD,
      binValue: '443573',
      encryptedBin: 'encrypted-bin-value',
    });
  });

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.card?.onFieldValid?.({
      valid: true,
      type: PAYMENT_TYPE_CARD,
      fieldType: 'encryptedExpiryDate',
      endDigits: '7890',
    } as CardFieldValidData);
  });

  expect(result.current.creditCardIdentifiers).toBeUndefined();
});

it('calls onFieldValid with no endDigits value, valid flag as false and returns bin state', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.creditCardIdentifiers).toBeUndefined();

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.card?.onBinValue?.({
      type: PAYMENT_TYPE_CARD,
      binValue: '443573',
      encryptedBin: 'encrypted-bin-value',
    });
  });

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.card?.onFieldValid?.({
      valid: false,
      type: PAYMENT_TYPE_CARD,
      fieldType: FIELD_TYPE_ENCRYPTED_CARD_NUMBER,
    } as CardFieldValidData);
  });

  expect(result.current.creditCardIdentifiers).toBeUndefined();
});

it('calls stored card onConfigSuccess with iframe being configured and returns isLoading state as false', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.isLoading).toBe(true);

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.storedCard?.onConfigSuccess?.({
      iframesConfigured: true,
    } as CardConfigSuccessData);
  });

  expect(result.current.isLoading).toBe(false);
});

it('calls stored cardonConfigSuccess with iframe not being configured and returns isLoading state as true', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.isLoading).toBe(true);

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.storedCard?.onConfigSuccess?.({
      iframesConfigured: false,
    } as CardConfigSuccessData);
  });

  expect(result.current.isLoading).toBe(true);
});

it('calls stored card onBinValue with encrypted bin value and calls onFieldValid with valid encryptedCardNumber field and returns bin state', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.creditCardIdentifiers).toBeUndefined();

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.storedCard?.onBinValue?.({
      type: PAYMENT_TYPE_CARD,
      binValue: '443573',
      encryptedBin: 'encrypted-bin-value',
    });
  });

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.storedCard?.onFieldValid?.({
      valid: true,
      type: PAYMENT_TYPE_CARD,
      fieldType: FIELD_TYPE_ENCRYPTED_CARD_NUMBER,
      endDigits: '7890',
    } as CardFieldValidData);
  });

  expect(result.current.creditCardIdentifiers).toEqual({
    bin: '443573',
    tokenizedCreditCard: '443573xxxxxx7890',
  });
});

it('calls stored card onBinValue with encrypted bin value and returns bin state as null', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.creditCardIdentifiers).toBeUndefined();

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.storedCard?.onBinValue?.({
      type: PAYMENT_TYPE_CARD,
      binValue: '443573',
      encryptedBin: 'encrypted-bin-value',
    });
  });

  expect(result.current.creditCardIdentifiers).toBeUndefined();
});

it('calls stored card onBinValue with invalid card type and returns bin state as null', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.creditCardIdentifiers).toBeUndefined();

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.storedCard?.onBinValue?.({
      type: 'apple-pay',
      binValue: 'bin-value',
      encryptedBin: 'encrypted-bin-value',
    });
  });

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.storedCard?.onFieldValid?.({
      valid: true,
      type: PAYMENT_TYPE_CARD,
      fieldType: FIELD_TYPE_ENCRYPTED_CARD_NUMBER,
      endDigits: '7890',
    } as CardFieldValidData);
  });

  expect(result.current.creditCardIdentifiers).toBeUndefined();
});

it('calls stored card onBinValue with valid card type but no encryptedBin and returns bin state as null', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.creditCardIdentifiers).toBeUndefined();

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.storedCard?.onBinValue?.({
      type: 'apple-pay',
      binValue: 'bin-value',
    });
  });

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.storedCard?.onFieldValid?.({
      valid: true,
      type: PAYMENT_TYPE_CARD,
      fieldType: FIELD_TYPE_ENCRYPTED_CARD_NUMBER,
      endDigits: '7890',
    } as CardFieldValidData);
  });

  expect(result.current.creditCardIdentifiers).toBeUndefined();
});

it('calls stored card onFieldValid with valid encryptedCardNumber field and returns bin state as null', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.creditCardIdentifiers).toBeUndefined();

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.storedCard?.onFieldValid?.({
      valid: true,
      type: PAYMENT_TYPE_CARD,
      fieldType: FIELD_TYPE_ENCRYPTED_CARD_NUMBER,
      endDigits: '7890',
    } as CardFieldValidData);
  });

  expect(result.current.creditCardIdentifiers).toBeUndefined();
});

it('calls stored card onFieldValid with invalid card type and returns bin state', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.creditCardIdentifiers).toBeUndefined();

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.storedCard?.onBinValue?.({
      type: PAYMENT_TYPE_CARD,
      binValue: '443573',
      encryptedBin: 'encrypted-bin-value',
    });
  });

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.storedCard?.onFieldValid?.({
      valid: true,
      type: 'apple-pay',
      fieldType: FIELD_TYPE_ENCRYPTED_CARD_NUMBER,
      endDigits: '7890',
    } as CardFieldValidData);
  });

  expect(result.current.creditCardIdentifiers).toBeUndefined();
});

it('calls stored card onFieldValid with invalid field type and returns bin state', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.creditCardIdentifiers).toBeUndefined();

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.storedCard?.onBinValue?.({
      type: PAYMENT_TYPE_CARD,
      binValue: '443573',
      encryptedBin: 'encrypted-bin-value',
    });
  });

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.card?.onFieldValid?.({
      valid: true,
      type: PAYMENT_TYPE_CARD,
      fieldType: 'encryptedExpiryDate',
      endDigits: '7890',
    } as CardFieldValidData);
  });

  expect(result.current.creditCardIdentifiers).toBeUndefined();
});

it('calls stored card onFieldValid with no endDigits value, valid flag as false and returns bin state', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.creditCardIdentifiers).toBeUndefined();

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.storedCard?.onBinValue?.({
      type: PAYMENT_TYPE_CARD,
      binValue: '443573',
      encryptedBin: 'encrypted-bin-value',
    });
  });

  act(() => {
    result.current.dropInConfiguration?.paymentMethodsConfiguration?.storedCard?.onFieldValid?.({
      valid: false,
      type: PAYMENT_TYPE_CARD,
      fieldType: FIELD_TYPE_ENCRYPTED_CARD_NUMBER,
    } as CardFieldValidData);
  });

  expect(result.current.creditCardIdentifiers).toBeUndefined();
});

it('calls onReady and returns isDropInReady state as true', () => {
  const { result } = renderHook(() => useDropInConfiguration());

  expect(result.current.isDropInReady).toBe(false);

  act(() => {
    result.current.dropInConfiguration?.onReady?.();
  });

  expect(result.current.isDropInReady).toBe(true);
});
